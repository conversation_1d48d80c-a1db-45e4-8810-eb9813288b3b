import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { Tool, ToolResult } from '../types';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const stat = promisify(fs.stat);
const readdir = promisify(fs.readdir);

export class FileTool implements Tool {
  name = 'file';
  description = 'Read, write, and manage files and directories. Can read file contents, create directories, check file stats, and list directory contents.';
  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['read', 'write', 'append', 'delete', 'exists', 'stat', 'mkdir', 'list', 'copy', 'move'],
        description: 'The file operation to perform'
      },
      path: {
        type: 'string',
        description: 'The file or directory path'
      },
      content: {
        type: 'string',
        description: 'Content to write (for write/append actions)'
      },
      encoding: {
        type: 'string',
        description: 'File encoding (default: utf8)',
        default: 'utf8'
      },
      recursive: {
        type: 'boolean',
        description: 'Create parent directories if they don\'t exist (for mkdir)',
        default: false
      },
      destination: {
        type: 'string',
        description: 'Destination path (for copy/move actions)'
      }
    },
    required: ['action', 'path']
  };

  async execute(args: {
    action: string;
    path: string;
    content?: string;
    encoding?: string;
    recursive?: boolean;
    destination?: string;
  }): Promise<ToolResult> {
    try {
      const { action, path: filePath, content, encoding = 'utf8', recursive = false, destination } = args;

      switch (action) {
        case 'read':
          return await this.readFile(filePath, encoding);

        case 'write':
          return await this.writeFile(filePath, content || '', encoding);

        case 'append':
          return await this.appendFile(filePath, content || '', encoding);

        case 'delete':
          return await this.deleteFile(filePath);

        case 'exists':
          return await this.fileExists(filePath);

        case 'stat':
          return await this.getFileStat(filePath);

        case 'mkdir':
          return await this.createDirectory(filePath, recursive);

        case 'list':
          return await this.listDirectory(filePath);

        case 'copy':
          if (!destination) {
            throw new Error('Destination path required for copy action');
          }
          return await this.copyFile(filePath, destination);

        case 'move':
          if (!destination) {
            throw new Error('Destination path required for move action');
          }
          return await this.moveFile(filePath, destination);

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'File operation failed'
      };
    }
  }

  private async readFile(filePath: string, encoding: string): Promise<ToolResult> {
    try {
      const content = await readFile(filePath, encoding as BufferEncoding);
      return {
        success: true,
        output: content,
        metadata: {
          path: filePath,
          size: content.length,
          encoding
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to read file: ${error.message}`
      };
    }
  }

  private async writeFile(filePath: string, content: string, encoding: string): Promise<ToolResult> {
    try {
      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        await mkdir(dir, { recursive: true });
      }

      await writeFile(filePath, content, encoding as BufferEncoding);
      return {
        success: true,
        output: `File written successfully: ${filePath}`,
        metadata: {
          path: filePath,
          size: content.length,
          encoding
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to write file: ${error.message}`
      };
    }
  }

  private async appendFile(filePath: string, content: string, encoding: string): Promise<ToolResult> {
    try {
      await fs.promises.appendFile(filePath, content, encoding as BufferEncoding);
      return {
        success: true,
        output: `Content appended to file: ${filePath}`,
        metadata: {
          path: filePath,
          appendedSize: content.length,
          encoding
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to append to file: ${error.message}`
      };
    }
  }

  private async deleteFile(filePath: string): Promise<ToolResult> {
    try {
      const stats = await stat(filePath);

      if (stats.isDirectory()) {
        await fs.promises.rmdir(filePath, { recursive: true });
        return {
          success: true,
          output: `Directory deleted: ${filePath}`,
          metadata: { path: filePath, type: 'directory' }
        };
      } else {
        await fs.promises.unlink(filePath);
        return {
          success: true,
          output: `File deleted: ${filePath}`,
          metadata: { path: filePath, type: 'file' }
        };
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to delete: ${error.message}`
      };
    }
  }

  private async fileExists(filePath: string): Promise<ToolResult> {
    try {
      await stat(filePath);
      return {
        success: true,
        output: `File exists: ${filePath}`,
        metadata: { path: filePath, exists: true }
      };
    } catch (error) {
      return {
        success: true,
        output: `File does not exist: ${filePath}`,
        metadata: { path: filePath, exists: false }
      };
    }
  }

  private async getFileStat(filePath: string): Promise<ToolResult> {
    try {
      const stats = await stat(filePath);
      const statInfo = {
        path: filePath,
        size: stats.size,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        created: stats.birthtime,
        modified: stats.mtime,
        accessed: stats.atime,
        permissions: stats.mode.toString(8)
      };

      return {
        success: true,
        output: JSON.stringify(statInfo, null, 2),
        metadata: statInfo
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to get file stats: ${error.message}`
      };
    }
  }

  private async createDirectory(dirPath: string, recursive: boolean): Promise<ToolResult> {
    try {
      await mkdir(dirPath, { recursive });
      return {
        success: true,
        output: `Directory created: ${dirPath}`,
        metadata: { path: dirPath, recursive }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to create directory: ${error.message}`
      };
    }
  }

  private async listDirectory(dirPath: string): Promise<ToolResult> {
    try {
      const files = await readdir(dirPath, { withFileTypes: true });
      const fileList = files.map(file => ({
        name: file.name,
        type: file.isDirectory() ? 'directory' : 'file',
        path: path.join(dirPath, file.name)
      }));

      return {
        success: true,
        output: JSON.stringify(fileList, null, 2),
        metadata: {
          path: dirPath,
          count: files.length,
          files: fileList
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to list directory: ${error.message}`
      };
    }
  }

  private async copyFile(sourcePath: string, destPath: string): Promise<ToolResult> {
    try {
      await fs.promises.copyFile(sourcePath, destPath);
      return {
        success: true,
        output: `File copied from ${sourcePath} to ${destPath}`,
        metadata: { source: sourcePath, destination: destPath }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to copy file: ${error.message}`
      };
    }
  }

  private async moveFile(sourcePath: string, destPath: string): Promise<ToolResult> {
    try {
      await fs.promises.rename(sourcePath, destPath);
      return {
        success: true,
        output: `File moved from ${sourcePath} to ${destPath}`,
        metadata: { source: sourcePath, destination: destPath }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to move file: ${error.message}`
      };
    }
  }
}
