import Anthropic from '@anthropic-ai/sdk';
import { Message, Tool, ModelConfig, StreamingResponse } from '../../types';

export class AnthropicProvider {
  private client: Anthropic;

  constructor(apiKey: string) {
    this.client = new Anthropic({
      apiKey: api<PERSON><PERSON>
    });
  }

  async generateResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig
  ): Promise<StreamingResponse> {
    try {
      const anthropicMessages = this.convertMessages(messages);
      const anthropicTools = this.convertTools(tools);

      // Extract system message
      const systemMessage = messages.find(m => m.role === 'system')?.content || '';
      const userMessages = anthropicMessages;

      const response = await this.client.messages.create({
        model: config.model,
        max_tokens: config.maxTokens || 4096,
        temperature: config.temperature,
        system: systemMessage,
        messages: userMessages,
        tools: anthropicTools.length > 0 ? anthropicTools : undefined,
        tool_choice: anthropicTools.length > 0 ? { type: 'auto' } : undefined
      });

      let content = '';
      let toolCalls: any[] = [];

      for (const block of response.content) {
        if (block.type === 'text') {
          content += block.text;
        } else if (block.type === 'tool_use') {
          toolCalls.push({
            id: block.id,
            type: 'function',
            function: {
              name: block.name,
              arguments: JSON.stringify(block.input)
            }
          });
        }
      }

      return {
        content,
        done: true,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
      };
    } catch (error) {
      throw new Error(`Anthropic API error: ${error}`);
    }
  }

  async streamResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    try {
      const anthropicMessages = this.convertMessages(messages);
      const anthropicTools = this.convertTools(tools);

      const systemMessage = messages.find(m => m.role === 'system')?.content || '';
      const userMessages = anthropicMessages;

      const stream = await this.client.messages.create({
        model: config.model,
        max_tokens: config.maxTokens || 4096,
        temperature: config.temperature,
        system: systemMessage,
        messages: userMessages,
        tools: anthropicTools.length > 0 ? anthropicTools : undefined,
        tool_choice: anthropicTools.length > 0 ? { type: 'auto' } : undefined,
        stream: true
      });

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          onChunk(chunk.delta.text);
        }
      }
    } catch (error) {
      throw new Error(`Anthropic streaming error: ${error}`);
    }
  }

  private convertMessages(messages: Message[]): Anthropic.Messages.MessageParam[] {
    return messages
      .filter(msg => msg.role !== 'system') // System messages handled separately
      .map(msg => {
        switch (msg.role) {
          case 'user':
            return {
              role: 'user',
              content: msg.content
            };
          case 'assistant':
            const content: any[] = [];

            if (msg.content) {
              content.push({
                type: 'text',
                text: msg.content
              });
            }

            if (msg.tool_calls) {
              for (const toolCall of msg.tool_calls) {
                content.push({
                  type: 'tool_use',
                  id: toolCall.id,
                  name: toolCall.function.name,
                  input: JSON.parse(toolCall.function.arguments)
                });
              }
            }

            return {
              role: 'assistant',
              content
            };
          case 'function':
            return {
              role: 'user',
              content: [
                {
                  type: 'tool_result',
                  tool_use_id: msg.name || 'unknown',
                  content: msg.content
                }
              ]
            };
          default:
            throw new Error(`Unsupported message role: ${msg.role}`);
        }
      });
  }

  private convertTools(tools: Tool[]): Anthropic.Messages.Tool[] {
    return tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      input_schema: tool.parameters
    }));
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.client.messages.create({
        model: 'claude-3-haiku-20240307',
        max_tokens: 10,
        messages: [{ role: 'user', content: 'test' }]
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    // Anthropic doesn't have a models endpoint, return known models
    return [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
  }
}
