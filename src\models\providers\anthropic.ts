import Anthropic from '@anthropic-ai/sdk';
import { Message, Tool, ModelConfig, StreamingResponse } from '../../types';

export class AnthropicProvider {
  private client: Anthropic;
  private maxRetries: number = 3;
  private baseDelay: number = 1000;

  constructor(apiKey: string) {
    this.client = new Anthropic({
      apiKey: apiKey,
      timeout: 60000, // 60 seconds timeout
      maxRetries: 0, // Handle retries manually
      defaultHeaders: {
        'User-Agent': 'KritrimaAI/1.0.0'
      }
    });
  }

  async generateResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig
  ): Promise<StreamingResponse> {
    return await this.executeWithRetry(async () => {
      const anthropicMessages = this.convertMessages(messages);
      const anthropicTools = this.convertTools(tools);

      // Extract system message
      const systemMessage = messages.find(m => m.role === 'system')?.content || '';
      const userMessages = anthropicMessages;

      const response = await this.client.messages.create({
        model: config.model,
        max_tokens: config.maxTokens || 4096,
        temperature: config.temperature,
        system: systemMessage,
        messages: userMessages,
        tools: anthropicTools.length > 0 ? anthropicTools : undefined,
        tool_choice: anthropicTools.length > 0 ? { type: 'auto' } : undefined
      });

      let content = '';
      let toolCalls: any[] = [];

      for (const block of response.content) {
        if (block.type === 'text') {
          content += block.text;
        } else if (block.type === 'tool_use') {
          toolCalls.push({
            id: block.id,
            type: 'function',
            function: {
              name: block.name,
              arguments: JSON.stringify(block.input)
            }
          });
        }
      }

      return {
        content,
        done: true,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
      };
    });
  }

  async streamResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    try {
      const anthropicMessages = this.convertMessages(messages);
      const anthropicTools = this.convertTools(tools);

      const systemMessage = messages.find(m => m.role === 'system')?.content || '';
      const userMessages = anthropicMessages;

      const stream = await this.client.messages.create({
        model: config.model,
        max_tokens: config.maxTokens || 4096,
        temperature: config.temperature,
        system: systemMessage,
        messages: userMessages,
        tools: anthropicTools.length > 0 ? anthropicTools : undefined,
        tool_choice: anthropicTools.length > 0 ? { type: 'auto' } : undefined,
        stream: true
      });

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          onChunk(chunk.delta.text);
        }
      }
    } catch (error) {
      throw new Error(`Anthropic streaming error: ${error}`);
    }
  }

  private convertMessages(messages: Message[]): Anthropic.Messages.MessageParam[] {
    return messages
      .filter(msg => msg.role !== 'system') // System messages handled separately
      .map(msg => {
        switch (msg.role) {
          case 'user':
            return {
              role: 'user',
              content: msg.content
            };
          case 'assistant':
            const content: any[] = [];

            if (msg.content) {
              content.push({
                type: 'text',
                text: msg.content
              });
            }

            if (msg.tool_calls) {
              for (const toolCall of msg.tool_calls) {
                content.push({
                  type: 'tool_use',
                  id: toolCall.id,
                  name: toolCall.function.name,
                  input: JSON.parse(toolCall.function.arguments)
                });
              }
            }

            return {
              role: 'assistant',
              content
            };
          case 'function':
            return {
              role: 'user',
              content: [
                {
                  type: 'tool_result',
                  tool_use_id: msg.name || 'unknown',
                  content: msg.content
                }
              ]
            };
          default:
            throw new Error(`Unsupported message role: ${msg.role}`);
        }
      });
  }

  private convertTools(tools: Tool[]): Anthropic.Messages.Tool[] {
    return tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      input_schema: tool.parameters
    }));
  }

  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain types of errors
        if (this.isNonRetryableError(error)) {
          throw this.enhanceError(error);
        }

        // If this was the last attempt, throw the error
        if (attempt === this.maxRetries) {
          throw this.enhanceError(error);
        }

        // Calculate delay with exponential backoff
        const delay = this.baseDelay * Math.pow(2, attempt);
        console.warn(`Anthropic API attempt ${attempt + 1} failed, retrying in ${delay}ms...`);
        await this.sleep(delay);
      }
    }

    throw this.enhanceError(lastError!);
  }

  private isNonRetryableError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    const errorCode = error?.code;

    // Don't retry on authentication errors
    if (errorMessage.includes('unauthorized') || errorMessage.includes('invalid api key')) {
      return true;
    }

    // Don't retry on quota exceeded errors
    if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
      return true;
    }

    // Don't retry on invalid request errors
    if (errorMessage.includes('bad request') || errorCode === 400) {
      return true;
    }

    return false;
  }

  private enhanceError(error: any): Error {
    const errorMessage = error?.message || 'Unknown error';

    if (errorMessage.includes('unauthorized') || errorMessage.includes('invalid api key')) {
      return new Error('Authentication error: Invalid Anthropic API key. Please check your ANTHROPIC_API_KEY in the .env file.');
    }

    if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
      return new Error('Rate limit exceeded: You have exceeded your Anthropic API quota. Please wait or upgrade your plan.');
    }

    if (errorMessage.includes('timeout')) {
      return new Error('Connection timeout: The request to Anthropic API timed out. Please try again.');
    }

    return new Error(`Anthropic API error: ${errorMessage}`);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.executeWithRetry(async () => {
        return await this.client.messages.create({
          model: 'claude-3-haiku-20240307',
          max_tokens: 10,
          messages: [{ role: 'user', content: 'test' }]
        });
      });
      return true;
    } catch (error) {
      console.warn(`Anthropic connection test failed: ${error}`);
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    // Anthropic doesn't have a models endpoint, return known models
    return [
      'claude-3-5-sonnet-20241022',
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
  }
}
