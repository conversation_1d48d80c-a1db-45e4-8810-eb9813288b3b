import * as fs from 'fs';
import * as path from 'path';
import { Tool, ToolResult } from '../types';

export class EditTool implements Tool {
  name = 'edit';
  description = 'Edit files with precise line-based operations. Can insert, replace, delete lines, and apply patches to existing files.';
  parameters = {
    type: 'object' as const,
    properties: {
      action: {
        type: 'string',
        enum: ['replace', 'insert', 'delete', 'patch', 'find_replace'],
        description: 'The edit operation to perform'
      },
      path: {
        type: 'string',
        description: 'The file path to edit'
      },
      line_start: {
        type: 'number',
        description: 'Starting line number (1-based)'
      },
      line_end: {
        type: 'number',
        description: 'Ending line number (1-based, for replace/delete)'
      },
      content: {
        type: 'string',
        description: 'New content to insert or replace with'
      },
      find: {
        type: 'string',
        description: 'Text to find (for find_replace action)'
      },
      replace: {
        type: 'string',
        description: 'Text to replace with (for find_replace action)'
      },
      global: {
        type: 'boolean',
        description: 'Replace all occurrences (for find_replace)',
        default: false
      },
      backup: {
        type: 'boolean',
        description: 'Create backup before editing',
        default: true
      }
    },
    required: ['action', 'path']
  };

  async execute(args: {
    action: string;
    path: string;
    line_start?: number;
    line_end?: number;
    content?: string;
    find?: string;
    replace?: string;
    global?: boolean;
    backup?: boolean;
  }): Promise<ToolResult> {
    try {
      const { action, path: filePath, backup = true } = args;

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        return {
          success: false,
          output: '',
          error: `File does not exist: ${filePath}`
        };
      }

      // Create backup if requested
      if (backup) {
        await this.createBackup(filePath);
      }

      switch (action) {
        case 'replace':
          return await this.replaceLines(args);

        case 'insert':
          return await this.insertLines(args);

        case 'delete':
          return await this.deleteLines(args);

        case 'find_replace':
          return await this.findReplace(args);

        case 'patch':
          return await this.applyPatch(args);

        default:
          throw new Error(`Unknown action: ${action}`);
      }
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Edit operation failed'
      };
    }
  }

  private async replaceLines(args: {
    path: string;
    line_start?: number;
    line_end?: number;
    content?: string;
  }): Promise<ToolResult> {
    const { path: filePath, line_start, line_end, content = '' } = args;

    if (!line_start) {
      throw new Error('line_start is required for replace action');
    }

    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const lines = fileContent.split('\n');

      const startIdx = line_start - 1; // Convert to 0-based;
      const endIdx = line_end ? line_end - 1 : startIdx;

      if (startIdx < 0 || startIdx >= lines.length) {
        throw new Error(`Invalid line number: ${line_start}`);
      }

      // Replace lines
      const newLines = content.split('\n');
      lines.splice(startIdx, endIdx - startIdx + 1, ...newLines);

      const newContent = lines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');

      return {
        success: true,
        output: `Replaced lines ${line_start}${line_end ? `-${line_end}` : ''} in ${filePath}`,
        metadata: {
          path: filePath,
          linesReplaced: endIdx - startIdx + 1,
          newLines: newLines.length
        }
      };
    } catch (error: any) {
      throw new Error(`Failed to replace lines: ${error.message}`);
    }
  }

  private async insertLines(args: {
    path: string;
    line_start?: number;
    content?: string;
  }): Promise<ToolResult> {
    const { path: filePath, line_start, content = '' } = args;

    if (!line_start) {
      throw new Error('line_start is required for insert action');
    }

    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const lines = fileContent.split('\n');

      const insertIdx = line_start - 1; // Convert to 0-based;

      if (insertIdx < 0 || insertIdx > lines.length) {
        throw new Error(`Invalid line number: ${line_start}`);
      }

      // Insert lines
      const newLines = content.split('\n');
      lines.splice(insertIdx, 0, ...newLines);

      const newContent = lines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');

      return {
        success: true,
        output: `Inserted ${newLines.length} lines at line ${line_start} in ${filePath}`,
        metadata: {
          path: filePath,
          insertPosition: line_start,
          linesInserted: newLines.length
        }
      };
    } catch (error: any) {
      throw new Error(`Failed to insert lines: ${error.message}`);
    }
  }

  private async deleteLines(args: {
    path: string;
    line_start?: number;
    line_end?: number;
  }): Promise<ToolResult> {
    const { path: filePath, line_start, line_end } = args;

    if (!line_start) {
      throw new Error('line_start is required for delete action');
    }

    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const lines = fileContent.split('\n');

      const startIdx = line_start - 1; // Convert to 0-based;
      const endIdx = line_end ? line_end - 1 : startIdx;

      if (startIdx < 0 || startIdx >= lines.length) {
        throw new Error(`Invalid line number: ${line_start}`);
      }

      // Delete lines
      const deletedLines = lines.splice(startIdx, endIdx - startIdx + 1);

      const newContent = lines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');

      return {
        success: true,
        output: `Deleted lines ${line_start}${line_end ? `-${line_end}` : ''} from ${filePath}`,
        metadata: {
          path: filePath,
          linesDeleted: deletedLines.length,
          deletedContent: deletedLines.join('\n')
        }
      };
    } catch (error: any) {
      throw new Error(`Failed to delete lines: ${error.message}`);
    }
  }

  private async findReplace(args: {
    path: string;
    find?: string;
    replace?: string;
    global?: boolean;
  }): Promise<ToolResult> {
    const { path: filePath, find, replace = '', global = false } = args;

    if (!find) {
      throw new Error('find text is required for find_replace action');
    }

    try {
      const fileContent = fs.readFileSync(filePath, 'utf8');

      let newContent: string;
      let replacements = 0;

      if (global) {
        const regex = new RegExp(this.escapeRegex(find), 'g');
        newContent = fileContent.replace(regex, () => {
          replacements++;
          return replace;
        });
      } else {
        const index = fileContent.indexOf(find);
        if (index !== -1) {
          newContent = fileContent.substring(0, index) + replace + fileContent.substring(index + find.length);
          replacements = 1;
        } else {
          newContent = fileContent;
        }
      }

      if (replacements > 0) {
        fs.writeFileSync(filePath, newContent, 'utf8');
      }

      return {
        success: true,
        output: `Made ${replacements} replacement(s) in ${filePath}`,
        metadata: {
          path: filePath,
          replacements,
          find,
          replace,
          global
        }
      };
    } catch (error: any) {
      throw new Error(`Failed to find/replace: ${error.message}`);
    }
  }

  private async applyPatch(args: {
    path: string;
    content?: string;
  }): Promise<ToolResult> {
    const { path: filePath, content } = args;

    if (!content) {
      throw new Error('patch content is required for patch action');
    }

    try {
      // Simple patch application - this is a basic implementation
      // In a real scenario, you might want to use a proper patch library
      const lines = content.split('\n');
      const fileContent = fs.readFileSync(filePath, 'utf8');
      let fileLines = fileContent.split('\n');

      for (const line of lines) {
        if (line.startsWith('@@')) {
          // Parse hunk header
          const match = line.match(/@@ -(\d+),?\d* \+(\d+),?\d* @@/);
          if (match) {
            // This is a simplified patch parser
            // Real implementation would be more complex
            continue;
          }
        } else if (line.startsWith('-')) {
          // Remove line
          const lineToRemove = line.substring(1);
          const index = fileLines.indexOf(lineToRemove);
          if (index !== -1) {
            fileLines.splice(index, 1);
          }
        } else if (line.startsWith('+')) {
          // Add line
          const lineToAdd = line.substring(1);
          fileLines.push(lineToAdd);
        }
      }

      const newContent = fileLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');

      return {
        success: true,
        output: `Applied patch to ${filePath}`,
        metadata: {
          path: filePath,
          patchLines: lines.length
        }
      };
    } catch (error: any) {
      throw new Error(`Failed to apply patch: ${error.message}`);
    }
  }

  private async createBackup(filePath: string): Promise<void> {
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.copyFileSync(filePath, backupPath);
  }

  private escapeRegex(text: string): string {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}
