import { OpenAI } from 'openai';
import { Message, Tool, ModelConfig, StreamingResponse } from '../../types';

export class DeepseekProvider {
  private client: OpenAI;
  private maxRetries: number = 3;
  private baseDelay: number = 1000; // 1 second
  private maxDelay: number = 10000; // 10 seconds

  constructor(apiKey: string, baseURL?: string) {
    this.client = new OpenAI({
      apiKey: apiKey,
      baseURL: baseURL || process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1',
      timeout: 60000, // 60 seconds timeout
      maxRetries: 0 // We'll handle retries manually for better control
    });
  }

  async generateResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig
  ): Promise<StreamingResponse> {
    // Validate message sequence before conversion
    this.validateMessageSequence(messages);

    const openaiMessages = this.convertMessages(messages);
    const openaiTools = this.convertTools(tools);

    return await this.executeWithRetry(async () => {
      const response = await this.client.chat.completions.create({
        model: config.model,
        messages: openaiMessages,
        tools: openaiTools.length > 0 ? openaiTools : undefined,
        tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
        temperature: config.temperature,
        max_tokens: config.maxTokens,
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stream: false
      });

      const choice = response.choices[0];
      if (!choice) {
        throw new Error('No response from Deepseek');
      }

      return {
        content: choice.message.content || '',
        done: true,
        toolCalls: choice.message.tool_calls?.map(tc => ({
          id: tc.id,
          type: 'function' as const,
          function: {
            name: tc.function.name,
            arguments: tc.function.arguments
          }
        }))
      };
    });
  }

  async streamResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    // Validate message sequence before conversion
    this.validateMessageSequence(messages);

    const openaiMessages = this.convertMessages(messages);
    const openaiTools = this.convertTools(tools);

    await this.executeWithRetry(async () => {
      const stream = await this.client.chat.completions.create({
        model: config.model,
        messages: openaiMessages,
        tools: openaiTools.length > 0 ? openaiTools : undefined,
        tool_choice: openaiTools.length > 0 ? 'auto' : undefined,
        temperature: config.temperature,
        max_tokens: config.maxTokens,
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stream: true
      });

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;
        if (delta?.content) {
          onChunk(delta.content);
        }
      }
    });
  }

  private convertMessages(messages: Message[]): any[] {
    return messages.map(msg => {
      switch (msg.role) {
        case 'system':
          return {
            role: 'system',
            content: msg.content
          };
        case 'user':
          return {
            role: 'user',
            content: msg.content
          };
        case 'assistant':
          const assistantMsg: any = {
            role: 'assistant',
            content: msg.content
          };

          if (msg.tool_calls) {
            assistantMsg.tool_calls = msg.tool_calls.map(tc => ({
              id: tc.id,
              type: 'function',
              function: {
                name: tc.function.name,
                arguments: tc.function.arguments
              }
            }));
          }

          return assistantMsg;
        case 'function':
          return {
            role: 'tool',
            tool_call_id: msg.name || 'unknown', // msg.name should contain the tool_call_id
            content: msg.content
          };
        default:
          throw new Error(`Unsupported message role: ${msg.role}`);
      }
    });
  }

  private convertTools(tools: Tool[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  private validateMessageSequence(messages: Message[]): void {
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];

      // Check if tool result messages follow assistant messages with tool_calls
      if (message.role === 'function') {
        // Find the preceding assistant message with tool_calls
        let foundToolCall = false;
        for (let j = i - 1; j >= 0; j--) {
          const prevMessage = messages[j];
          if (prevMessage.role === 'assistant' && prevMessage.tool_calls) {
            // Check if this tool result corresponds to one of the tool calls
            const toolCallIds = prevMessage.tool_calls.map(tc => tc.id);
            if (toolCallIds.includes(message.name || '')) {
              foundToolCall = true;
              break;
            }
          }
          // Stop looking if we hit another assistant message without tool_calls
          if (prevMessage.role === 'assistant' && !prevMessage.tool_calls) {
            break;
          }
        }

        if (!foundToolCall) {
          console.warn(`Warning: Tool result message without corresponding tool call: ${message.name}`);
          // Don't throw error, just warn - let the API handle it
        }
      }
    }
  }

  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain types of errors
        if (this.isNonRetryableError(error)) {
          throw this.enhanceError(error);
        }

        // If this was the last attempt, throw the error
        if (attempt === this.maxRetries) {
          throw this.enhanceError(error);
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.baseDelay * Math.pow(2, attempt),
          this.maxDelay
        );

        console.warn(`Deepseek API attempt ${attempt + 1} failed, retrying in ${delay}ms...`);
        await this.sleep(delay);
      }
    }

    throw this.enhanceError(lastError!);
  }

  private isNonRetryableError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    const errorCode = error?.code;

    // Don't retry on authentication errors
    if (errorMessage.includes('unauthorized') || errorMessage.includes('invalid api key')) {
      return true;
    }

    // Don't retry on quota exceeded errors
    if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
      return true;
    }

    // Don't retry on invalid request errors
    if (errorMessage.includes('bad request') || errorCode === 400) {
      return true;
    }

    return false;
  }

  private enhanceError(error: any): Error {
    const errorMessage = error?.message || 'Unknown error';
    const errorCode = error?.code;

    // Provide more helpful error messages
    if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('ECONNREFUSED')) {
      return new Error('Connection error: Unable to reach Deepseek API. Please check your internet connection and try again.');
    }

    if (errorMessage.includes('timeout')) {
      return new Error('Connection timeout: The request to Deepseek API timed out. Please try again.');
    }

    if (errorMessage.includes('unauthorized') || errorMessage.includes('invalid api key')) {
      return new Error('Authentication error: Invalid Deepseek API key. Please check your DEEPSEEK_API_KEY in the .env file.');
    }

    if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
      return new Error('Rate limit exceeded: You have exceeded your Deepseek API quota. Please wait or upgrade your plan.');
    }

    if (errorCode === 400) {
      return new Error(`Bad request: ${errorMessage}. Please check your request parameters.`);
    }

    return new Error(`Deepseek API error: ${errorMessage}`);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.executeWithRetry(async () => {
        return await this.client.chat.completions.create({
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: 'test' }],
          max_tokens: 10
        });
      });
      return true;
    } catch (error) {
      console.warn(`Deepseek connection test failed: ${error}`);
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data.map(model => model.id).sort();
    } catch (error) {
      return ['deepseek-chat', 'deepseek-coder']; // fallback;
    }
  }
}
