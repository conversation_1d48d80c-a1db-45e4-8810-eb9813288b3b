import axios from 'axios';
import { Message, Tool, ModelConfig, StreamingResponse } from '../../types';

export class OllamaProvider {
  private baseUrl: string;
  private maxRetries: number = 3;
  private baseDelay: number = 1000;
  private timeout: number = 60000; // 60 seconds

  constructor(baseUrl: string = 'http://localhost:11434') {
    this.baseUrl = baseUrl;
  }

  async generateResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig
  ): Promise<StreamingResponse> {
    return await this.executeWithRetry(async () => {
      const ollamaMessages = this.convertMessages(messages);
      const ollamaTools = this.convertTools(tools);

      const response = await axios.post(`${this.baseUrl}/api/chat`, {
        model: config.model,
        messages: ollamaMessages,
        tools: ollamaTools.length > 0 ? ollamaTools : undefined,
        options: {
          temperature: config.temperature,
          top_p: config.topP,
          num_predict: config.maxTokens
        },
        stream: false
      }, {
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'KritrimaAI/1.0.0'
        }
      });

      const data = response.data;

      // Parse tool calls if present
      let toolCalls: any[] = [];
      if (data.message?.tool_calls) {
        toolCalls = data.message.tool_calls.map((tc: any) => ({
          id: tc.function?.name || Math.random().toString(36),
          type: 'function',
          function: {
            name: tc.function?.name,
            arguments: JSON.stringify(tc.function?.arguments || {})
          }
        }));
      }

      return {
        content: data.message?.content || '',
        done: true,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
      };
    });
  }

  async streamResponse(
    messages: Message[],
    tools: Tool[],
    config: ModelConfig,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    try {
      const ollamaMessages = this.convertMessages(messages);
      const ollamaTools = this.convertTools(tools);

      const response = await axios.post(`${this.baseUrl}/api/chat`, {
        model: config.model,
        messages: ollamaMessages,
        tools: ollamaTools.length > 0 ? ollamaTools : undefined,
        options: {
          temperature: config.temperature,
          top_p: config.topP,
          num_predict: config.maxTokens
        },
        stream: true
      }, {
        responseType: 'stream'
      });

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n').filter(line => line.trim());

        for (const line of lines) {
          try {
            const data = JSON.parse(line);
            if (data.message?.content) {
              onChunk(data.message.content);
            }
          } catch (parseError) {
            // Ignore parsing errors for incomplete chunks
          }
        }
      });

      return new Promise((resolve, reject) => {
        response.data.on('end', resolve);
        response.data.on('error', reject);
      });
    } catch (error) {
      throw new Error(`Ollama streaming error: ${error}`);
    }
  }

  private convertMessages(messages: Message[]): any[] {
    return messages.map(msg => {
      switch (msg.role) {
        case 'system':
          return {
            role: 'system',
            content: msg.content
          };
        case 'user':
          return {
            role: 'user',
            content: msg.content
          };
        case 'assistant':
          const assistantMsg: any = {
            role: 'assistant',
            content: msg.content
          };

          if (msg.tool_calls) {
            assistantMsg.tool_calls = msg.tool_calls.map(tc => ({
              function: {
                name: tc.function.name,
                arguments: JSON.parse(tc.function.arguments)
              }
            }));
          }

          return assistantMsg;
        case 'function':
          return {
            role: 'tool',
            content: msg.content
          };
        default:
          throw new Error(`Unsupported message role: ${msg.role}`);
      }
    });
  }

  private convertTools(tools: Tool[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        // Don't retry on certain types of errors
        if (this.isNonRetryableError(error)) {
          throw this.enhanceError(error);
        }

        // If this was the last attempt, throw the error
        if (attempt === this.maxRetries) {
          throw this.enhanceError(error);
        }

        // Calculate delay with exponential backoff
        const delay = this.baseDelay * Math.pow(2, attempt);
        console.warn(`Ollama API attempt ${attempt + 1} failed, retrying in ${delay}ms...`);
        await this.sleep(delay);
      }
    }

    throw this.enhanceError(lastError!);
  }

  private isNonRetryableError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    const errorCode = error?.response?.status;

    // Don't retry on model not found errors
    if (errorMessage.includes('model not found') || errorCode === 404) {
      return true;
    }

    // Don't retry on invalid request errors
    if (errorMessage.includes('bad request') || errorCode === 400) {
      return true;
    }

    return false;
  }

  private enhanceError(error: any): Error {
    const errorMessage = error?.message || 'Unknown error';
    const errorCode = error?.response?.status;

    if (errorMessage.includes('ECONNREFUSED') || errorMessage.includes('ENOTFOUND')) {
      return new Error('Connection error: Unable to reach Ollama server. Please ensure Ollama is running on ' + this.baseUrl);
    }

    if (errorMessage.includes('timeout')) {
      return new Error('Connection timeout: The request to Ollama server timed out. Please try again.');
    }

    if (errorMessage.includes('model not found') || errorCode === 404) {
      return new Error('Model not found: The requested model is not available. Please pull the model first or check the model name.');
    }

    return new Error(`Ollama API error: ${errorMessage}`);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.executeWithRetry(async () => {
        return await axios.get(`${this.baseUrl}/api/tags`, {
          timeout: 10000 // 10 second timeout for health check
        });
      });
      return true;
    } catch (error) {
      console.warn(`Ollama connection test failed: ${error}`);
      return false;
    }
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.executeWithRetry(async () => {
        return await axios.get(`${this.baseUrl}/api/tags`, {
          timeout: this.timeout
        });
      });
      return response.data.models?.map((model: any) => model.name) || [];
    } catch (error) {
      return ['llama2', 'codellama', 'mistral']; // fallback
    }
  }

  async pullModel(modelName: string): Promise<void> {
    try {
      await this.executeWithRetry(async () => {
        return await axios.post(`${this.baseUrl}/api/pull`, {
          name: modelName
        }, {
          timeout: 300000 // 5 minutes for model pulling
        });
      });
    } catch (error) {
      throw new Error(`Failed to pull model ${modelName}: ${error}`);
    }
  }

  async deleteModel(modelName: string): Promise<void> {
    try {
      await this.executeWithRetry(async () => {
        return await axios.delete(`${this.baseUrl}/api/delete`, {
          data: { name: modelName },
          timeout: this.timeout
        });
      });
    } catch (error) {
      throw new Error(`Failed to delete model ${modelName}: ${error}`);
    }
  }

  // Check if Ollama server is running and healthy
  async isServerHealthy(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/version`, {
        timeout: 5000
      });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  // Get server information
  async getServerInfo(): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/version`, {
        timeout: this.timeout
      });
      return response.data;
    } catch (error) {
      return null;
    }
  }
}
