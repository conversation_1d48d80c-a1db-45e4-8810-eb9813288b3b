import { Tool } from '../types';
import { ShellTool } from './shell';
import { FileTool } from './file';
import { EditTool } from './edit';
import { WriteTool } from './write';
import { GrepTool } from './grep';
import { WebTool } from './web';
import { AnalysisTool } from './analysis';

export class ToolManager {
  private tools: Map<string, Tool> = new Map();

  constructor() {
    this.initializeTools();
  }

  private initializeTools(): void {
    const tools = [
      new ShellTool(),
      new FileTool(),
      new EditTool(),
      new WriteTool(),
      new GrepTool(),
      new WebTool(),
      new AnalysisTool()
    ];

    for (const tool of tools) {
      this.tools.set(tool.name, tool);
    }
  }

  getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  getAvailableTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  async executeTool(name: string, args: any): Promise<any> {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }

    return await tool.execute(args);
  }

  registerTool(tool: Tool): void {
    this.tools.set(tool.name, tool);
  }

  unregisterTool(name: string): void {
    this.tools.delete(name);
  }

  listTools(): string[] {
    return Array.from(this.tools.keys());
  }

  getToolDescription(name: string): string | undefined {
    const tool = this.tools.get(name);
    return tool?.description;
  }

  getToolParameters(name: string): any {
    const tool = this.tools.get(name);
    return tool?.parameters;
  }

  // Enhanced tool execution with better error handling and result processing
  async executeToolWithContext(name: string, args: any, context?: any): Promise<any> {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool not found: ${name}`);
    }

    try {
      const startTime = Date.now();
      const result = await tool.execute(args);
      const executionTime = Date.now() - startTime;

      // Enhance result with execution metadata
      return {
        ...result,
        metadata: {
          ...result.metadata,
          toolName: name,
          executionTime,
          timestamp: new Date().toISOString(),
          args: args
        }
      };
    } catch (error) {
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          toolName: name,
          executionTime: 0,
          timestamp: new Date().toISOString(),
          args: args
        }
      };
    }
  }

  // Get tool execution statistics
  getToolStats(): Record<string, any> {
    const stats: Record<string, any> = {};

    for (const [name, tool] of this.tools) {
      stats[name] = {
        name: tool.name,
        description: tool.description,
        parameters: Object.keys(tool.parameters.properties || {}),
        requiredParams: tool.parameters.required || []
      };
    }

    return stats;
  }

  // Validate tool arguments
  validateToolArgs(toolName: string, args: any): { valid: boolean; errors: string[] } {
    const tool = this.tools.get(toolName);
    if (!tool) {
      return { valid: false, errors: [`Tool not found: ${toolName}`] };
    }

    const errors: string[] = [];
    const required = tool.parameters.required || [];
    const properties = tool.parameters.properties || {};

    // Check required parameters
    for (const param of required) {
      if (!(param in args)) {
        errors.push(`Missing required parameter: ${param}`);
      }
    }

    // Check parameter types (basic validation)
    for (const [param, value] of Object.entries(args)) {
      if (properties[param]) {
        const expectedType = properties[param].type;
        const actualType = typeof value;

        if (expectedType === 'string' && actualType !== 'string') {
          errors.push(`Parameter ${param} should be a string, got ${actualType}`);
        } else if (expectedType === 'number' && actualType !== 'number') {
          errors.push(`Parameter ${param} should be a number, got ${actualType}`);
        } else if (expectedType === 'boolean' && actualType !== 'boolean') {
          errors.push(`Parameter ${param} should be a boolean, got ${actualType}`);
        }
      }
    }

    return { valid: errors.length === 0, errors };
  }
}
