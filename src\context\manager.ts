import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';
import { ContextFile, ContextManager as IContextManager } from '../types';

export class ContextManager implements IContextManager {
  private files: Map<string, ContextFile> = new Map();
  private maxTokens: number;
  private readonly TOKENS_PER_CHAR = 0.25; // Approximate tokens per character

  constructor(maxTokens: number = 2000000) { // 2M tokens default
    this.maxTokens = maxTokens;
  }

  async addFile(filePath: string): Promise<void> {
    const resolvedPath = path.resolve(filePath);

    if (fs.statSync(resolvedPath).isDirectory()) {
      await this.addDirectory(resolvedPath);
    } else {
      await this.addSingleFile(resolvedPath);
    }

    this.optimize();
  }

  private async addDirectory(dirPath: string): Promise<void> {
    const patterns = [
      '**/*.ts', '**/*.js', '**/*.tsx', '**/*.jsx',
      '**/*.py', '**/*.java', '**/*.cpp', '**/*.c',
      '**/*.cs', '**/*.go', '**/*.rs', '**/*.php',
      '**/*.rb', '**/*.swift', '**/*.kt', '**/*.scala',
      '**/*.json', '**/*.yaml', '**/*.yml', '**/*.xml',
      '**/*.md', '**/*.txt', '**/*.sql', '**/*.sh',
      '**/*.dockerfile', '**/Dockerfile', '**/*.env'
    ];

    const ignorePatterns = [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.git/**',
      '**/coverage/**',
      '**/*.log',
      '**/*.tmp',
      '**/*.temp'
    ];

    for (const pattern of patterns) {
      const files = await glob(pattern, {
        cwd: dirPath,
        ignore: ignorePatterns,
        absolute: true
      });

      for (const file of files) {
        try {
          await this.addSingleFile(file);
        } catch (error) {
          console.warn(`Failed to add file ${file}: ${error}`);
        }
      }
    }
  }

  private async addSingleFile(filePath: string): Promise<void> {
    try {
      const stats = fs.statSync(filePath);
      const content = fs.readFileSync(filePath, 'utf-8');

      // Skip binary files and very large files
      if (this.isBinaryFile(content) || stats.size > 1000000) { // 1MB limit per file
        return;
      }

      const contextFile: ContextFile = {
        path: filePath,
        content,
        size: stats.size,
        lastModified: stats.mtime,
        language: this.detectLanguage(filePath)
      };

      this.files.set(filePath, contextFile);
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error}`);
    }
  }

  removeFile(filePath: string): void {
    const resolvedPath = path.resolve(filePath);
    this.files.delete(resolvedPath);
  }

  getContext(): ContextFile[] {
    return Array.from(this.files.values());
  }

  getTotalTokens(): number {
    let totalChars = 0;
    for (const file of this.files.values()) {
      totalChars += file.content.length;
    }
    return Math.ceil(totalChars * this.TOKENS_PER_CHAR);
  }

  optimize(): void {
    const currentTokens = this.getTotalTokens();

    if (currentTokens <= this.maxTokens) {
      return;
    }

    // Sort files by importance (smaller files, recently modified, code files first)
    const sortedFiles = Array.from(this.files.entries()).sort(([, a], [, b]) => {
      // Prioritize code files
      const aIsCode = this.isCodeFile(a.path);
      const bIsCode = this.isCodeFile(b.path);
      if (aIsCode !== bIsCode) {
        return bIsCode ? 1 : -1;
      }

      // Prioritize recently modified files
      const timeDiff = b.lastModified.getTime() - a.lastModified.getTime();
      if (Math.abs(timeDiff) > 86400000) { // 1 day
        return timeDiff > 0 ? 1 : -1;
      }

      // Prioritize smaller files
      return a.size - b.size;
    });

    // Remove files until we're under the token limit
    let tokensToRemove = currentTokens - this.maxTokens;
    for (const [filePath, file] of sortedFiles.reverse()) {
      if (tokensToRemove <= 0) break;

      const fileTokens = Math.ceil(file.content.length * this.TOKENS_PER_CHAR);
      this.files.delete(filePath);
      tokensToRemove -= fileTokens;
    }
  }

  async loadWorkingDirectory(workingDir: string): Promise<void> {
    if (!fs.existsSync(workingDir)) {
      throw new Error(`Working directory does not exist: ${workingDir}`);
    }

    await this.addDirectory(workingDir);
  }

  private isBinaryFile(content: string): boolean {
    // Check for null bytes which indicate binary content
    return content.includes('\0');
  }

  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.cs': 'csharp',
      '.go': 'go',
      '.rs': 'rust',
      '.php': 'php',
      '.rb': 'ruby',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
      '.json': 'json',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.xml': 'xml',
      '.md': 'markdown',
      '.sql': 'sql',
      '.sh': 'bash',
      '.dockerfile': 'dockerfile'
    };

    return languageMap[ext] || 'text';
  }

  private isCodeFile(filePath: string): boolean {
    const codeExtensions = [
      '.ts', '.tsx', '.js', '.jsx', '.py', '.java',
      '.cpp', '.c', '.cs', '.go', '.rs', '.php',
      '.rb', '.swift', '.kt', '.scala'
    ];

    const ext = path.extname(filePath).toLowerCase();
    return codeExtensions.includes(ext);
  }

  // Get context summary for AI models
  getContextSummary(): string {
    const files = this.getContext();
    const summary = {
      totalFiles: files.length,
      totalTokens: this.getTotalTokens(),
      languages: {} as Record<string, number>,
      recentFiles: files
        .sort((a, b) => b.lastModified.getTime() - a.lastModified.getTime())
        .slice(0, 10)
        .map(f => ({ path: f.path, language: f.language }))
    };

    // Count files by language
    for (const file of files) {
      const lang = file.language || 'unknown';
      summary.languages[lang] = (summary.languages[lang] || 0) + 1;
    }

    return JSON.stringify(summary, null, 2);
  }

  // Get specific files by pattern
  getFilesByPattern(pattern: string): ContextFile[] {
    const regex = new RegExp(pattern, 'i');
    return this.getContext().filter(file =>
      regex.test(file.path) || regex.test(file.content)
    );
  }

  // Get files by language
  getFilesByLanguage(language: string): ContextFile[] {
    return this.getContext().filter(file => file.language === language);
  }
}
