# Sample Python file with intentional errors for testing Kritrima AI

import os
import sys

# Missing colon
def broken_function():
    return "This function is missing a colon"

# Mixed indentation (tabs and spaces)
def mixed_indentation():
  if True:
        return "mixed indentation"

# TODO comment
# TODO: Implement proper logging

# Hardcoded password
PASSWORD = "super_secret_password_123"

# Long line exceeding 120 characters
very_long_variable_name = "This is an extremely long string that definitely exceeds the maximum recommended line length and should be flagged"

# Unused import
import json  # This import is never used

# Undefined variable
def use_undefined():
    return undefined_variable + 10

# Missing docstring
def calculate_area(radius):
    return 3.14159 * radius * radius

# Bare except clause
try:
    risky_operation()
except:
    pass

# FIXME comment
# FIXME: This needs better error handling

# Hardcoded file path
def read_config():
    with open("/home/<USER>/config.txt", "r") as f:
        return f.read()

# SQL injection vulnerability
def get_user(user_id):
    query = f"SELECT * FROM users WHERE id = {user_id}"
    return query

# Magic numbers
def calculate_tax(amount):
    return amount * 0.08  # What tax rate is this?

# Inconsistent naming
user_name = "john"
userAge = 25
UserEmail = "<EMAIL>"

# Dead code
def never_called():
    return "This function is never called"

# Complex nested conditions
def complex_logic(user):
    if user:
        if user.get('active'):
            if user.get('permissions'):
                if user['permissions'].get('read'):
                    if user['permissions'].get('write'):
                        return True
    return False

# Missing type hints
def add_numbers(a, b):
    return a + b

# Mutable default argument
def append_to_list(item, target_list=[]):
    target_list.append(item)
    return target_list

# Global variable modification
global_counter = 0

def increment_counter():
    global global_counter
    global_counter += 1

# Catching too broad exception
try:
    some_operation()
except Exception:
    print("Something went wrong")

# Using eval (security risk)
def dangerous_eval(user_input):
    return eval(user_input)

# Trailing whitespace (invisible)
example_var = "test"

# Missing space around operators
result=a+b*c

# Inconsistent quotes
name = 'John'
surname = "Doe"

# Lambda that could be a function
square = lambda x: x * x

# Using deprecated function
import imp  # imp module is deprecated

# Not using context manager for file operations
def bad_file_handling():
    f = open("file.txt", "r")
    content = f.read()
    f.close()  # Should use 'with' statement
    return content
