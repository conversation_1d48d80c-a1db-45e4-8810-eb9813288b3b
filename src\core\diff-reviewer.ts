import * as fs from 'fs';
import * as path from 'path';
import { createTwoFilesPatch, applyPatch, parsePatch } from 'diff';
import { DiffChange, ReviewSandbox } from '../types';

export class DiffReviewer {
  private sandboxes: Map<string, ReviewSandbox> = new Map();
  private sandboxDir: string;

  constructor(sandboxDir: string = '.kritrima-sandbox') {
    this.sandboxDir = sandboxDir;
    this.ensureSandboxDir();
  }

  private ensureSandboxDir(): void {
    if (!fs.existsSync(this.sandboxDir)) {
      fs.mkdirSync(this.sandboxDir, { recursive: true });
    }
  }

  createSandbox(changes: DiffChange[]): ReviewSandbox {
    const sandboxId = this.generateSandboxId();
    const sandbox: ReviewSandbox = {
      id: sandboxId,
      changes,
      status: 'pending',
      createdAt: new Date()
    };

    this.sandboxes.set(sandboxId, sandbox);
    this.createSandboxFiles(sandbox);

    return sandbox;
  }

  private createSandboxFiles(sandbox: ReviewSandbox): void {
    const sandboxPath = path.join(this.sandboxDir, sandbox.id);

    if (!fs.existsSync(sandboxPath)) {
      fs.mkdirSync(sandboxPath, { recursive: true });
    }

    // Create diff files for each change
    for (let i = 0; i < sandbox.changes.length; i++) {
      const change = sandbox.changes[i];
      const diffPath = path.join(sandboxPath, `change_${i}.diff`);

      let diff: string;

      switch (change.type) {
        case 'add':
          diff = this.createAddDiff(change);
          break;
        case 'modify':
          diff = this.createModifyDiff(change);
          break;
        case 'remove':
          diff = this.createRemoveDiff(change);
          break;
        default:
          continue;
      }

      fs.writeFileSync(diffPath, diff, 'utf8');
    }

    // Create summary file
    const summaryPath = path.join(sandboxPath, 'summary.json');
    fs.writeFileSync(summaryPath, JSON.stringify({
      id: sandbox.id,
      status: sandbox.status,
      createdAt: sandbox.createdAt,
      changesCount: sandbox.changes.length,
      changes: sandbox.changes.map(c => ({
        type: c.type,
        path: c.path,
        lineNumber: c.lineNumber
      }))
    }, null, 2), 'utf8');
  }

  private createAddDiff(change: DiffChange): string {
    const newContent = change.content;
    return createTwoFilesPatch(
      '/dev/null',
      change.path,
      '',
      newContent,
      '',
      ''
    );
  }

  private createModifyDiff(change: DiffChange): string {
    const originalContent = change.originalContent || '';
    const newContent = change.content;

    return createTwoFilesPatch(
      change.path,
      change.path,
      originalContent,
      newContent,
      '',
      ''
    );
  }

  private createRemoveDiff(change: DiffChange): string {
    const originalContent = change.originalContent || '';
    return createTwoFilesPatch(
      change.path,
      '/dev/null',
      originalContent,
      '',
      '',
      ''
    );
  }

  reviewChanges(sandboxId: string): {
    sandbox: ReviewSandbox;
    analysis: ChangeAnalysis;
  } {
    const sandbox = this.sandboxes.get(sandboxId);
    if (!sandbox) {
      throw new Error(`Sandbox not found: ${sandboxId}`);
    }

    const analysis = this.analyzeChanges(sandbox.changes);

    return { sandbox, analysis };
  }

  private analyzeChanges(changes: DiffChange[]): ChangeAnalysis {
    const analysis: ChangeAnalysis = {
      totalChanges: changes.length,
      addedFiles: 0,
      modifiedFiles: 0,
      removedFiles: 0,
      linesAdded: 0,
      linesRemoved: 0,
      riskLevel: 'low',
      issues: [],
      recommendations: []
    };

    for (const change of changes) {
      switch (change.type) {
        case 'add':
          analysis.addedFiles++;
          analysis.linesAdded += change.content.split('\n').length;
          break;
        case 'modify':
          analysis.modifiedFiles++;
          const oldLines = (change.originalContent || '').split('\n').length;
          const newLines = change.content.split('\n').length;
          if (newLines > oldLines) {
            analysis.linesAdded += newLines - oldLines;
          } else {
            analysis.linesRemoved += oldLines - newLines;
          }
          break;
        case 'remove':
          analysis.removedFiles++;
          analysis.linesRemoved += (change.originalContent || '').split('\n').length;
          break;
      }

      // Check for potential issues
      this.checkForIssues(change, analysis);
    }

    // Determine risk level
    analysis.riskLevel = this.calculateRiskLevel(analysis);

    // Generate recommendations
    analysis.recommendations = this.generateRecommendations(analysis);

    return analysis;
  }

  private checkForIssues(change: DiffChange, analysis: ChangeAnalysis): void {
    const content = change.content;

    // Check for potential security issues
    if (content.includes('password') || content.includes('secret') || content.includes('key')) {
      analysis.issues.push({
        type: 'security',
        severity: 'high',
        message: `Potential hardcoded secret in ${change.path}`,
        line: change.lineNumber
      });
    }

    // Check for console.log statements
    if (content.includes('console.log')) {
      analysis.issues.push({
        type: 'code-quality',
        severity: 'low',
        message: `Console.log statement in ${change.path}`,
        line: change.lineNumber
      });
    }

    // Check for TODO comments
    if (content.includes('TODO') || content.includes('FIXME')) {
      analysis.issues.push({
        type: 'maintenance',
        severity: 'low',
        message: `TODO/FIXME comment in ${change.path}`,
        line: change.lineNumber
      });
    }

    // Check for large changes
    if (content.split('\n').length > 100) {
      analysis.issues.push({
        type: 'complexity',
        severity: 'medium',
        message: `Large change (>100 lines) in ${change.path}`,
        line: change.lineNumber
      });
    }
  }

  private calculateRiskLevel(analysis: ChangeAnalysis): 'low' | 'medium' | 'high' {
    let riskScore = 0;

    // File changes contribute to risk
    riskScore += analysis.addedFiles * 1;
    riskScore += analysis.modifiedFiles * 2;
    riskScore += analysis.removedFiles * 3;

    // Line changes contribute to risk
    riskScore += Math.floor((analysis.linesAdded + analysis.linesRemoved) / 50);

    // Issues contribute to risk
    for (const issue of analysis.issues) {
      switch (issue.severity) {
        case 'high':
          riskScore += 10;
          break;
        case 'medium':
          riskScore += 5;
          break;
        case 'low':
          riskScore += 1;
          break;
      }
    }

    if (riskScore >= 20) return 'high';
    if (riskScore >= 10) return 'medium';
    return 'low';
  }

  private generateRecommendations(analysis: ChangeAnalysis): string[] {
    const recommendations: string[] = [];

    if (analysis.issues.some(i => i.type === 'security')) {
      recommendations.push('Review security-related changes carefully');
      recommendations.push('Consider using environment variables for secrets');
    }

    if (analysis.linesAdded + analysis.linesRemoved > 500) {
      recommendations.push('Consider breaking this into smaller changes');
      recommendations.push('Add comprehensive tests for large changes');
    }

    if (analysis.issues.some(i => i.type === 'code-quality')) {
      recommendations.push('Remove debug statements before merging');
      recommendations.push('Run code quality checks');
    }

    if (analysis.riskLevel === 'high') {
      recommendations.push('Request additional code review');
      recommendations.push('Test thoroughly in staging environment');
    }

    return recommendations;
  }

  approveSandbox(sandboxId: string): void {
    const sandbox = this.sandboxes.get(sandboxId);
    if (!sandbox) {
      throw new Error(`Sandbox not found: ${sandboxId}`);
    }

    sandbox.status = 'approved';
  }

  rejectSandbox(sandboxId: string): void {
    const sandbox = this.sandboxes.get(sandboxId);
    if (!sandbox) {
      throw new Error(`Sandbox not found: ${sandboxId}`);
    }

    sandbox.status = 'rejected';
  }

  applyChanges(sandboxId: string): void {
    const sandbox = this.sandboxes.get(sandboxId);
    if (!sandbox) {
      throw new Error(`Sandbox not found: ${sandboxId}`);
    }

    if (sandbox.status !== 'approved') {
      throw new Error('Sandbox must be approved before applying changes');
    }

    for (const change of sandbox.changes) {
      this.applyChange(change);
    }
  }

  private applyChange(change: DiffChange): void {
    switch (change.type) {
      case 'add':
        this.createFile(change.path, change.content);
        break;
      case 'modify':
        this.modifyFile(change.path, change.content);
        break;
      case 'remove':
        this.removeFile(change.path);
        break;
    }
  }

  private createFile(filePath: string, content: string): void {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(filePath, content, 'utf8');
  }

  private modifyFile(filePath: string, content: string): void {
    fs.writeFileSync(filePath, content, 'utf8');
  }

  private removeFile(filePath: string): void {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }

  getSandbox(sandboxId: string): ReviewSandbox | undefined {
    return this.sandboxes.get(sandboxId);
  }

  listSandboxes(): ReviewSandbox[] {
    return Array.from(this.sandboxes.values());
  }

  deleteSandbox(sandboxId: string): void {
    const sandboxPath = path.join(this.sandboxDir, sandboxId);
    if (fs.existsSync(sandboxPath)) {
      fs.rmSync(sandboxPath, { recursive: true });
    }
    this.sandboxes.delete(sandboxId);
  }

  private generateSandboxId(): string {
    return `sandbox_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

interface ChangeAnalysis {
  totalChanges: number;
  addedFiles: number;
  modifiedFiles: number;
  removedFiles: number;
  linesAdded: number;
  linesRemoved: number;
  riskLevel: 'low' | 'medium' | 'high';
  issues: Issue[];
  recommendations: string[];
}

interface Issue {
  type: 'security' | 'code-quality' | 'maintenance' | 'complexity';
  severity: 'low' | 'medium' | 'high';
  message: string;
  line?: number;
}
