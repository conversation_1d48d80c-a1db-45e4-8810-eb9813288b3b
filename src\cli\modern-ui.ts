import chalk from 'chalk';
import * as readline from 'readline';

export interface Theme {
  primary: typeof chalk;
  secondary: typeof chalk;
  success: typeof chalk;
  warning: typeof chalk;
  error: typeof chalk;
  info: typeof chalk;
  accent: typeof chalk;
  muted: typeof chalk;
}

export interface UIConfig {
  theme: string;
  animations: boolean;
  compactMode: boolean;
  showTimestamps: boolean;
  showProgressBars: boolean;
}

export class ModernUI {
  private theme: Theme;
  private config: UIConfig;
  private terminalWidth: number;
  private terminalHeight: number;

  constructor(config: Partial<UIConfig> = {}) {
    this.config = {
      theme: 'default',
      animations: true,
      compactMode: false,
      showTimestamps: true,
      showProgressBars: true,
      ...config
    };

    this.terminalWidth = process.stdout.columns || 80;
    this.terminalHeight = process.stdout.rows || 24;
    this.theme = this.getTheme(this.config.theme);

    // Listen for terminal resize
    process.stdout.on('resize', () => {
      this.terminalWidth = process.stdout.columns || 80;
      this.terminalHeight = process.stdout.rows || 24;
    });
  }

  private getTheme(themeName: string): Theme {
    const themes: Record<string, Theme> = {
      default: {
        primary: chalk.cyan,
        secondary: chalk.blue,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.gray,
        accent: chalk.magenta,
        muted: chalk.dim
      },
      dark: {
        primary: chalk.white,
        secondary: chalk.gray,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.dim,
        accent: chalk.cyan,
        muted: chalk.dim.gray
      },
      matrix: {
        primary: chalk.green,
        secondary: chalk.greenBright,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.dim.green,
        accent: chalk.greenBright,
        muted: chalk.dim.green
      },
      ocean: {
        primary: chalk.blue,
        secondary: chalk.cyan,
        success: chalk.green,
        warning: chalk.yellow,
        error: chalk.red,
        info: chalk.blueBright,
        accent: chalk.magenta,
        muted: chalk.dim.blue
      }
    };

    return themes[themeName] || themes.default;
  }

  public setTheme(themeName: string): void {
    this.config.theme = themeName;
    this.theme = this.getTheme(themeName);
  }

  public createBox(content: string, title?: string, style: 'solid' | 'double' | 'rounded' = 'solid'): string {
    const lines = content.split('\n');
    const maxContentWidth = Math.max(...lines.map(line => this.stripAnsi(line).length));
    const boxWidth = Math.min(maxContentWidth + 4, this.terminalWidth - 2);
    const contentWidth = boxWidth - 4;

    const chars = {
      solid: { h: '─', v: '│', tl: '┌', tr: '┐', bl: '└', br: '┘' },
      double: { h: '═', v: '║', tl: '╔', tr: '╗', bl: '╚', br: '╝' },
      rounded: { h: '─', v: '│', tl: '╭', tr: '╮', bl: '╰', br: '╯' }
    };

    const { h, v, tl, tr, bl, br } = chars[style];

    let result = '';

    // Top border
    if (title) {
      const titlePadding = Math.max(0, boxWidth - title.length - 4);
      const leftPad = Math.floor(titlePadding / 2);
      const rightPad = titlePadding - leftPad;
      result += this.theme.primary(tl + h.repeat(leftPad) + ` ${title} ` + h.repeat(rightPad) + tr) + '\n';
    } else {
      result += this.theme.primary(tl + h.repeat(boxWidth - 2) + tr) + '\n';
    }

    // Content
    for (const line of lines) {
      const paddedLine = line.padEnd(contentWidth);
      result += this.theme.primary(v) + ' ' + paddedLine + ' ' + this.theme.primary(v) + '\n';
    }

    // Bottom border
    result += this.theme.primary(bl + h.repeat(boxWidth - 2) + br);

    return result;
  }

  public createProgressBar(current: number, total: number, width: number = 30, showPercentage: boolean = true): string {
    const percentage = Math.round((current / total) * 100);
    const filled = Math.round((current / total) * width);
    const empty = width - filled;

    const bar = this.theme.success('█'.repeat(filled)) + this.theme.muted('░'.repeat(empty));
    const percentText = showPercentage ? ` ${percentage}%` : '';

    return `[${bar}]${percentText}`;
  }

  public createTable(headers: string[], rows: string[][], options: {
    border?: boolean;
    padding?: number;
    maxWidth?: number;
  } = {}): string {
    const { border = true, padding = 1, maxWidth = this.terminalWidth - 4 } = options;

    if (rows.length === 0) return '';

    // Calculate column widths
    const colWidths = headers.map((header, i) => {
      const maxContentWidth = Math.max(
        header.length,
        ...rows.map(row => this.stripAnsi(row[i] || '').length)
      );
      return Math.min(maxContentWidth, Math.floor(maxWidth / headers.length) - padding * 2);
    });

    const totalWidth = colWidths.reduce((sum, width) => sum + width + padding * 2, 0) + headers.length + 1;

    let result = '';

    if (border) {
      result += this.theme.primary('┌' + colWidths.map(w => '─'.repeat(w + padding * 2)).join('┬') + '┐') + '\n';
    }

    // Headers
    const headerRow = headers.map((header, i) => {
      const truncated = header.length > colWidths[i] ? header.substring(0, colWidths[i] - 3) + '...' : header;
      return this.theme.accent(truncated.padEnd(colWidths[i]));
    }).join(border ? this.theme.primary('│') + ' '.repeat(padding) : ' '.repeat(padding * 2));

    result += (border ? this.theme.primary('│') + ' '.repeat(padding) : '') + headerRow +
              (border ? ' '.repeat(padding) + this.theme.primary('│') : '') + '\n';

    if (border) {
      result += this.theme.primary('├' + colWidths.map(w => '─'.repeat(w + padding * 2)).join('┼') + '┤') + '\n';
    }

    // Rows
    for (const row of rows) {
      const formattedRow = row.map((cell, i) => {
        const stripped = this.stripAnsi(cell || '');
        const truncated = stripped.length > colWidths[i] ? stripped.substring(0, colWidths[i] - 3) + '...' : stripped;
        return truncated.padEnd(colWidths[i]);
      }).join(border ? this.theme.primary('│') + ' '.repeat(padding) : ' '.repeat(padding * 2));

      result += (border ? this.theme.primary('│') + ' '.repeat(padding) : '') + formattedRow +
                (border ? ' '.repeat(padding) + this.theme.primary('│') : '') + '\n';
    }

    if (border) {
      result += this.theme.primary('└' + colWidths.map(w => '─'.repeat(w + padding * 2)).join('┴') + '┘');
    }

    return result;
  }

  public createStatusLine(items: Array<{ label: string; value: string; color?: keyof Theme }>): string {
    const maxWidth = this.terminalWidth - 4;
    let result = '';

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const color = item.color ? this.theme[item.color] : this.theme.info;
      const separator = i < items.length - 1 ? this.theme.muted(' • ') : '';

      const itemText = `${item.label}: ${color(item.value)}${separator}`;

      if (result.length + this.stripAnsi(itemText).length <= maxWidth) {
        result += itemText;
      } else {
        break;
      }
    }

    return result;
  }

  public createBanner(text: string, style: 'simple' | 'fancy' | 'gradient' = 'fancy'): string {
    const width = Math.min(text.length + 8, this.terminalWidth);
    const padding = Math.max(0, Math.floor((width - text.length) / 2));

    switch (style) {
      case 'simple':
        return this.theme.primary('='.repeat(width)) + '\n' +
               this.theme.primary(' '.repeat(padding) + text) + '\n' +
               this.theme.primary('='.repeat(width));

      case 'fancy':
        return this.theme.primary('╔' + '═'.repeat(width - 2) + '╗') + '\n' +
               this.theme.primary('║') + this.theme.accent(' '.repeat(padding) + text + ' '.repeat(width - text.length - padding - 2)) + this.theme.primary('║') + '\n' +
               this.theme.primary('╚' + '═'.repeat(width - 2) + '╝');

      case 'gradient':
        const colors = [chalk.red, chalk.yellow, chalk.green, chalk.cyan, chalk.blue, chalk.magenta];
        let gradientText = '';
        for (let i = 0; i < text.length; i++) {
          const colorIndex = Math.floor((i / text.length) * colors.length);
          gradientText += colors[colorIndex](text[i]);
        }
        return gradientText;

      default:
        return text;
    }
  }

  public clearScreen(): void {
    console.clear();
  }

  public moveCursor(x: number, y: number): void {
    process.stdout.write(`\x1b[${y};${x}H`);
  }

  public hideCursor(): void {
    process.stdout.write('\x1b[?25l');
  }

  public showCursor(): void {
    process.stdout.write('\x1b[?25h');
  }

  private stripAnsi(str: string): string {
    return str.replace(/\x1b\[[0-9;]*m/g, '');
  }

  public getTerminalSize(): { width: number; height: number } {
    return { width: this.terminalWidth, height: this.terminalHeight };
  }

  public createSpinner(text: string): any {
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let currentFrame = 0;
    let interval: NodeJS.Timeout;

    return {
      start: () => {
        process.stdout.write('\x1b[?25l'); // Hide cursor
        interval = setInterval(() => {
          process.stdout.write(`\r${this.theme.primary(frames[currentFrame])} ${text}`);
          currentFrame = (currentFrame + 1) % frames.length;
        }, 80);
      },
      stop: () => {
        clearInterval(interval);
        process.stdout.write('\r' + ' '.repeat(text.length + 2) + '\r');
        process.stdout.write('\x1b[?25h'); // Show cursor
      },
      succeed: (message?: string) => {
        clearInterval(interval);
        process.stdout.write(`\r${this.theme.success('✓')} ${message || text}\n`);
        process.stdout.write('\x1b[?25h'); // Show cursor
      },
      fail: (message?: string) => {
        clearInterval(interval);
        process.stdout.write(`\r${this.theme.error('✗')} ${message || text}\n`);
        process.stdout.write('\x1b[?25h'); // Show cursor
      }
    };
  }
}
