import { OpenAI } from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { AIModel, ModelConfig, Message, ContextFile, Tool, StreamingResponse } from '../types';
import { OpenAIProvider } from './providers/openai';
import { AnthropicProvider } from './providers/anthropic';
import { DeepseekProvider } from './providers/deepseek';
import { OllamaProvider } from './providers/ollama';

export class ModelManager {
  private providers: Map<string, any> = new Map();
  private models: AIModel[] = [];
  private defaultModel: string = '';

  constructor() {
    this.initializeProviders();
    this.loadAvailableModels();
    this.setDefaultModel();
  }

  private initializeProviders(): void {
    // Initialize providers with API keys from environment
    if (this.isValidApiKey(process.env.OPENAI_API_KEY)) {
      try {
        this.providers.set('openai', new OpenAIProvider(process.env.OPENAI_API_KEY!));
        console.log('✅ OpenAI provider initialized');
      } catch (error) {
        console.warn(`⚠️  Failed to initialize OpenAI provider: ${error}`);
      }
    }

    if (this.isValidApiKey(process.env.ANTHROPIC_API_KEY)) {
      try {
        this.providers.set('anthropic', new AnthropicProvider(process.env.ANTHROPIC_API_KEY!));
        console.log('✅ Anthropic provider initialized');
      } catch (error) {
        console.warn(`⚠️  Failed to initialize Anthropic provider: ${error}`);
      }
    }

    if (this.isValidApiKey(process.env.DEEPSEEK_API_KEY)) {
      try {
        this.providers.set('deepseek', new DeepseekProvider(
          process.env.DEEPSEEK_API_KEY!,
          process.env.DEEPSEEK_BASE_URL
        ));
        console.log('✅ Deepseek provider initialized');
      } catch (error) {
        console.warn(`⚠️  Failed to initialize Deepseek provider: ${error}`);
      }
    }

    // Ollama doesn't require API key for local models
    try {
      this.providers.set('ollama', new OllamaProvider());
      console.log('✅ Ollama provider initialized');
    } catch (error) {
      console.warn(`⚠️  Failed to initialize Ollama provider: ${error}`);
    }
  }

  private isValidApiKey(apiKey: string | undefined): boolean {
    if (!apiKey) return false;

    // Check for placeholder values
    const placeholders = [
      'your_openai_api_key_here',
      'your_anthropic_api_key_here',
      'your_deepseek_api_key_here',
      'sk-placeholder',
      'your_api_key_here'
    ];

    return !placeholders.includes(apiKey) && apiKey.length > 10;
  }

  private setDefaultModel(): void {
    // Set default model based on available providers
    const availableModels = this.models.filter(model => this.providers.has(model.provider));

    if (availableModels.length === 0) {
      console.warn('No AI providers available. Please configure API keys or start Ollama.');
      return;
    }

    // Priority order: OpenAI -> Anthropic -> Deepseek -> Ollama
    const preferredProviders = ['openai', 'anthropic', 'deepseek', 'ollama'];

    for (const provider of preferredProviders) {
      const model = availableModels.find(m => m.provider === provider);
      if (model) {
        this.defaultModel = model.name;
        console.log(`Default model set to: ${model.name}`);
        return;
      }
    }

    // Fallback to first available model
    this.defaultModel = availableModels[0].name;
    console.log(`Default model set to: ${availableModels[0].name}`);
  }

  private loadAvailableModels(): void {
    this.models = [
      // OpenAI Models
      {
        name: 'gpt-4-turbo-preview',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },
      {
        name: 'gpt-4',
        provider: 'openai',
        maxTokens: 8192,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },
      {
        name: 'gpt-3.5-turbo',
        provider: 'openai',
        maxTokens: 16385,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },

      // Anthropic Models
      {
        name: 'claude-3-opus-20240229',
        provider: 'anthropic',
        maxTokens: 200000,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },
      {
        name: 'claude-3-sonnet-20240229',
        provider: 'anthropic',
        maxTokens: 200000,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },
      {
        name: 'claude-3-haiku-20240307',
        provider: 'anthropic',
        maxTokens: 200000,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },

      // Deepseek Models
      {
        name: 'deepseek-chat',
        provider: 'deepseek',
        maxTokens: 32768,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },
      {
        name: 'deepseek-coder',
        provider: 'deepseek',
        maxTokens: 16384,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },

      // Ollama Models (common local models)
      {
        name: 'llama2',
        provider: 'ollama',
        maxTokens: 4096,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },
      {
        name: 'codellama',
        provider: 'ollama',
        maxTokens: 4096,
        supportsStreaming: true,
        supportsFunctionCalling: true
      },
      {
        name: 'mistral',
        provider: 'ollama',
        maxTokens: 8192,
        supportsStreaming: true,
        supportsFunctionCalling: true
      }
    ];
  }

  async getAvailableModels(): Promise<AIModel[]> {
    // Filter models based on available providers
    return this.models.filter(model => this.providers.has(model.provider));
  }

  async getModel(modelName: string): Promise<AIModel> {
    const model = this.models.find(m => m.name === modelName);
    if (!model) {
      throw new Error(`Model not found: ${modelName}`);
    }

    if (!this.providers.has(model.provider)) {
      throw new Error(`Provider not available: ${model.provider}`);
    }

    return model;
  }

  async getDefaultModel(): Promise<AIModel> {
    if (!this.defaultModel) {
      throw new Error('No default model available. Please configure API keys or start Ollama.');
    }
    return await this.getModel(this.defaultModel);
  }

  async getDefaultConfig(): Promise<ModelConfig> {
    if (!this.defaultModel) {
      throw new Error('No default model available. Please configure API keys or start Ollama.');
    }
    return {
      model: this.defaultModel,
      temperature: 0.7,
      maxTokens: 4096,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0
    };
  }

  async getConfigForModel(modelName: string): Promise<ModelConfig> {
    const model = await this.getModel(modelName);

    return {
      model: modelName,
      temperature: 0.7,
      maxTokens: Math.min(4096, model.maxTokens),
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0
    };
  }

  async generateResponse(
    messages: Message[],
    context: ContextFile[],
    tools: Tool[],
    config: ModelConfig
  ): Promise<StreamingResponse> {
    const model = await this.getModel(config.model);
    const provider = this.providers.get(model.provider);

    if (!provider) {
      throw new Error(`Provider not available: ${model.provider}`);
    }

    // Prepare context for the model
    const contextSummary = this.prepareContext(context);
    const systemMessage = this.buildSystemMessage(contextSummary, tools);

    // Add system message to the beginning
    const fullMessages = [systemMessage, ...messages];

    try {
      return await provider.generateResponse(fullMessages, tools, config);
    } catch (error) {
      console.warn(`Primary provider ${model.provider} failed: ${error}`);

      // Try fallback to other providers if available
      const fallbackProvider = await this.getFallbackProvider(model.provider);
      if (fallbackProvider) {
        console.log(`🔄 Attempting fallback to ${fallbackProvider.name}...`);
        try {
          const fallbackConfig = { ...config, model: fallbackProvider.model };
          return await fallbackProvider.provider.generateResponse(fullMessages, tools, fallbackConfig);
        } catch (fallbackError) {
          console.warn(`Fallback provider also failed: ${fallbackError}`);
        }
      }

      // If all else fails, throw the original error with enhanced message
      throw new Error(`All providers failed. Original error: ${error}`);
    }
  }

  private prepareContext(context: ContextFile[]): string {
    if (context.length === 0) {
      return 'No files in context.';
    }

    let contextStr = `Context includes ${context.length} files:\n\n`;

    for (const file of context.slice(0, 50)) { // Limit to 50 files in summary
      contextStr += `File: ${file.path} (${file.language})\n`;
      contextStr += `Size: ${file.size} bytes\n`;
      contextStr += `Content:\n${file.content.slice(0, 2000)}${file.content.length > 2000 ? '...' : ''}\n\n`;
    }

    if (context.length > 50) {
      contextStr += `... and ${context.length - 50} more files\n`;
    }

    return contextStr;
  }

  private buildSystemMessage(contextSummary: string, tools: Tool[]): Message {
    const toolDescriptions = tools.map(tool =>
      `${tool.name}: ${tool.description}`
    ).join('\n');

    return {
      role: 'system',
      content: `You are Kritrima AI, an advanced coding assistant with autonomous capabilities.

CONTEXT:
${contextSummary}

AVAILABLE TOOLS:
${toolDescriptions}

CAPABILITIES:
- Analyze and understand large codebases
- Execute shell commands autonomously
- Read, write, and edit files
- Search through code and documentation
- Detect and fix errors automatically
- Plan and execute complex development tasks

INSTRUCTIONS:
1. Always analyze the user's request thoroughly
2. Break down complex tasks into smaller steps
3. Use available tools to gather information and execute tasks
4. Provide detailed explanations of your actions
5. Ask for clarification when needed
6. Prioritize code quality and best practices
7. Handle errors gracefully and provide helpful feedback
8. When processing tool results, analyze them carefully and provide meaningful insights
9. If tool execution reveals issues or opportunities, suggest next steps
10. Always provide a comprehensive response after tool execution

TOOL EXECUTION GUIDELINES:
- Execute tools autonomously to complete tasks efficiently
- Always explain what you're doing and why
- After tool execution, analyze the results and provide insights
- If multiple tools are needed, execute them in logical sequence
- Summarize findings and provide actionable recommendations
- If tool results indicate errors or issues, suggest solutions

You have access to powerful tools for code analysis, file manipulation, and system interaction. Use them effectively to provide comprehensive assistance.`
    };
  }

  async streamResponse(
    messages: Message[],
    context: ContextFile[],
    tools: Tool[],
    config: ModelConfig,
    onChunk: (chunk: string) => void
  ): Promise<void> {
    const model = await this.getModel(config.model);
    const provider = this.providers.get(model.provider);

    if (!provider) {
      throw new Error(`Provider not available: ${model.provider}`);
    }

    const contextSummary = this.prepareContext(context);
    const systemMessage = this.buildSystemMessage(contextSummary, tools);
    const fullMessages = [systemMessage, ...messages];

    await provider.streamResponse(fullMessages, tools, config, onChunk);
  }

  private async getFallbackProvider(failedProvider: string): Promise<{ name: string, model: string, provider: any } | null> {
    // Define fallback order
    const fallbackOrder: Record<string, string[]> = {
      'deepseek': ['openai', 'anthropic', 'ollama'],
      'openai': ['deepseek', 'anthropic', 'ollama'],
      'anthropic': ['openai', 'deepseek', 'ollama'],
      'ollama': ['deepseek', 'openai', 'anthropic']
    };

    const fallbacks = fallbackOrder[failedProvider] || [];

    for (const providerName of fallbacks) {
      if (this.providers.has(providerName)) {
        const provider = this.providers.get(providerName);
        const availableModels = this.models.filter(m => m.provider === providerName);

        if (availableModels.length > 0) {
          // Test if the provider is working
          try {
            if (provider.testConnection) {
              const isWorking = await provider.testConnection();
              if (isWorking) {
                return {
                  name: providerName,
                  model: availableModels[0].name,
                  provider: provider
                };
              }
            } else {
              // If no test method, assume it's working
              return {
                name: providerName,
                model: availableModels[0].name,
                provider: provider
              };
            }
          } catch (error) {
            console.warn(`Fallback provider ${providerName} test failed: ${error}`);
            continue;
          }
        }
      }
    }

    return null;
  }

  async testAllProviders(): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};

    for (const [name, provider] of this.providers.entries()) {
      try {
        if (provider.testConnection) {
          results[name] = await provider.testConnection();
        } else {
          results[name] = true; // Assume working if no test method
        }
      } catch (error) {
        console.warn(`Provider ${name} test failed: ${error}`);
        results[name] = false;
      }
    }

    return results;
  }

  async getWorkingProviders(): Promise<string[]> {
    const results = await this.testAllProviders();
    return Object.entries(results)
      .filter(([_, isWorking]) => isWorking)
      .map(([name, _]) => name);
  }

  // Enhanced diagnostics for providers
  async getProviderDiagnostics(): Promise<Record<string, any>> {
    const diagnostics: Record<string, any> = {};

    for (const [name, provider] of this.providers.entries()) {
      try {
        const isWorking = provider.testConnection ? await provider.testConnection() : true;
        const models = this.models.filter(m => m.provider === name);

        diagnostics[name] = {
          status: isWorking ? 'working' : 'failed',
          models: models.map(m => m.name),
          modelCount: models.length,
          supportsStreaming: models.some(m => m.supportsStreaming),
          supportsFunctionCalling: models.some(m => m.supportsFunctionCalling)
        };

        // Add provider-specific diagnostics
        if (name === 'deepseek' && provider.getConnectionStatus) {
          const connectionStatus = provider.getConnectionStatus();
          diagnostics[name].connectionHealth = connectionStatus;
        }

        if (name === 'ollama' && provider.isServerHealthy) {
          const serverHealthy = await provider.isServerHealthy();
          diagnostics[name].serverHealthy = serverHealthy;

          if (provider.getServerInfo) {
            const serverInfo = await provider.getServerInfo();
            diagnostics[name].serverInfo = serverInfo;
          }
        }

      } catch (error) {
        diagnostics[name] = {
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error',
          models: [],
          modelCount: 0
        };
      }
    }

    return diagnostics;
  }

  // Get the best available provider for a task
  async getBestProvider(preferredProvider?: string): Promise<{ name: string, provider: any, model: string } | null> {
    const workingProviders = await this.getWorkingProviders();

    if (workingProviders.length === 0) {
      return null;
    }

    // If preferred provider is working, use it
    if (preferredProvider && workingProviders.includes(preferredProvider)) {
      const provider = this.providers.get(preferredProvider);
      const models = this.models.filter(m => m.provider === preferredProvider);
      if (provider && models.length > 0) {
        return {
          name: preferredProvider,
          provider,
          model: models[0].name
        };
      }
    }

    // Otherwise, use the first working provider in priority order
    const priorityOrder = ['deepseek', 'openai', 'anthropic', 'ollama'];

    for (const providerName of priorityOrder) {
      if (workingProviders.includes(providerName)) {
        const provider = this.providers.get(providerName);
        const models = this.models.filter(m => m.provider === providerName);
        if (provider && models.length > 0) {
          return {
            name: providerName,
            provider,
            model: models[0].name
          };
        }
      }
    }

    // Fallback to any working provider
    const firstWorking = workingProviders[0];
    const provider = this.providers.get(firstWorking);
    const models = this.models.filter(m => m.provider === firstWorking);

    if (provider && models.length > 0) {
      return {
        name: firstWorking,
        provider,
        model: models[0].name
      };
    }

    return null;
  }

  // Reinitialize a specific provider
  async reinitializeProvider(providerName: string): Promise<boolean> {
    try {
      switch (providerName) {
        case 'openai':
          if (this.isValidApiKey(process.env.OPENAI_API_KEY)) {
            this.providers.set('openai', new OpenAIProvider(process.env.OPENAI_API_KEY!));
            console.log('✅ OpenAI provider reinitialized');
            return true;
          }
          break;
        case 'anthropic':
          if (this.isValidApiKey(process.env.ANTHROPIC_API_KEY)) {
            this.providers.set('anthropic', new AnthropicProvider(process.env.ANTHROPIC_API_KEY!));
            console.log('✅ Anthropic provider reinitialized');
            return true;
          }
          break;
        case 'deepseek':
          if (this.isValidApiKey(process.env.DEEPSEEK_API_KEY)) {
            this.providers.set('deepseek', new DeepseekProvider(
              process.env.DEEPSEEK_API_KEY!,
              process.env.DEEPSEEK_BASE_URL
            ));
            console.log('✅ Deepseek provider reinitialized');
            return true;
          }
          break;
        case 'ollama':
          this.providers.set('ollama', new OllamaProvider());
          console.log('✅ Ollama provider reinitialized');
          return true;
      }
      return false;
    } catch (error) {
      console.warn(`Failed to reinitialize ${providerName} provider: ${error}`);
      return false;
    }
  }
}
