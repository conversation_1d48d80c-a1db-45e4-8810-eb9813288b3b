import { EventEmitter } from 'events';
import { ExecutionPlan, PlanStep, ToolResult } from '../types';
import { ToolManager } from '../tools/manager';

export class PlanExecutor extends EventEmitter {
  private toolManager: ToolManager;
  private currentPlan: ExecutionPlan | null = null;
  private isExecuting = false;

  constructor(toolManager: ToolManager) {
    super();
    this.toolManager = toolManager;
  }

  async executePlan(plan: ExecutionPlan): Promise<void> {
    if (this.isExecuting) {
      throw new Error('Another plan is already executing');
    }

    this.isExecuting = true;
    this.currentPlan = plan;

    try {
      this.emit('planStart', plan);

      // Sort steps by dependencies
      const sortedSteps = this.topologicalSort(plan.steps);

      for (const step of sortedSteps) {
        await this.executeStep(step);

        if (step.status === 'failed') {
          plan.status = 'failed';
          break;
        }
      }

      if (plan.status !== 'failed') {
        plan.status = 'completed';
      }

      this.emit('planComplete', plan);
    } catch (error) {
      plan.status = 'failed';
      this.emit('planError', error);
      throw error;
    } finally {
      this.isExecuting = false;
      this.currentPlan = null;
    }
  }

  private async executeStep(step: PlanStep): Promise<void> {
    try {
      step.status = 'executing';
      this.emit('stepStart', step);

      const tool = this.toolManager.getTool(step.tool);
      if (!tool) {
        throw new Error(`Tool not found: ${step.tool}`);
      }

      const result = await tool.execute(step.args);
      step.result = result;

      if (result.success) {
        step.status = 'completed';
        this.emit('stepComplete', step);
      } else {
        step.status = 'failed';
        this.emit('stepFailed', step);
      }
    } catch (error) {
      step.status = 'failed';
      step.result = {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      this.emit('stepFailed', step);
    }
  }

  private topologicalSort(steps: PlanStep[]): PlanStep[] {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const result: PlanStep[] = [];
    const stepMap = new Map(steps.map(step => [step.id, step]));

    const visit = (stepId: string) => {
      if (visiting.has(stepId)) {
        throw new Error(`Circular dependency detected involving step: ${stepId}`);
      }

      if (visited.has(stepId)) {
        return;
      }

      const step = stepMap.get(stepId);
      if (!step) {
        throw new Error(`Step not found: ${stepId}`);
      }

      visiting.add(stepId);

      // Visit dependencies first
      if (step.dependencies) {
        for (const depId of step.dependencies) {
          visit(depId);
        }
      }

      visiting.delete(stepId);
      visited.add(stepId);
      result.push(step);
    };

    // Visit all steps
    for (const step of steps) {
      if (!visited.has(step.id)) {
        visit(step.id);
      }
    }

    return result;
  }

  async executeStepsInParallel(steps: PlanStep[]): Promise<void> {
    const promises = steps.map(step => this.executeStep(step));
    await Promise.allSettled(promises);
  }

  getCurrentPlan(): ExecutionPlan | null {
    return this.currentPlan;
  }

  isCurrentlyExecuting(): boolean {
    return this.isExecuting;
  }

  async pauseExecution(): Promise<void> {
    // Implementation for pausing execution
    // This would require more complex state management
    this.emit('planPaused', this.currentPlan);
  }

  async resumeExecution(): Promise<void> {
    // Implementation for resuming execution
    this.emit('planResumed', this.currentPlan);
  }

  async cancelExecution(): Promise<void> {
    if (this.currentPlan) {
      this.currentPlan.status = 'failed';
      this.isExecuting = false;
      this.emit('planCancelled', this.currentPlan);
    }
  }

  // Create a plan from a high-level description
  createPlan(description: string, context: any): ExecutionPlan {
    const planId = this.generatePlanId();

    // This is a simplified plan creation
    // In a real implementation, this would use AI to break down the task
    const steps = this.breakDownTask(description, context);

    return {
      id: planId,
      steps,
      status: 'pending',
      createdAt: new Date()
    };
  }

  private breakDownTask(description: string, context: any): PlanStep[] {
    // Simplified task breakdown
    // In a real implementation, this would use AI to analyze the task
    const steps: PlanStep[] = [];

    // Example: if description mentions "create file"
    if (description.toLowerCase().includes('create file')) {
      steps.push({
        id: this.generateStepId(),
        description: 'Create new file',
        tool: 'write',
        args: {
          path: 'example.txt',
          content: 'Hello World'
        },
        status: 'pending'
      });
    }

    // Example: if description mentions "search"
    if (description.toLowerCase().includes('search')) {
      steps.push({
        id: this.generateStepId(),
        description: 'Search for pattern',
        tool: 'grep',
        args: {
          pattern: 'TODO',
          path: '.'
        },
        status: 'pending'
      });
    }

    return steps;
  }

  private generatePlanId(): string {
    return `plan_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateStepId(): string {
    return `step_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // Get execution statistics
  getExecutionStats(plan: ExecutionPlan): {
    total: number;
    completed: number;
    failed: number;
    pending: number;
    executing: number;
  } {
    const stats = {
      total: plan.steps.length,
      completed: 0,
      failed: 0,
      pending: 0,
      executing: 0
    };

    for (const step of plan.steps) {
      stats[step.status]++;
    }

    return stats;
  }

  // Retry failed steps
  async retryFailedSteps(plan: ExecutionPlan): Promise<void> {
    const failedSteps = plan.steps.filter(step => step.status === 'failed');

    for (const step of failedSteps) {
      step.status = 'pending';
      await this.executeStep(step);
    }
  }

  // Validate plan before execution
  validatePlan(plan: ExecutionPlan): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for circular dependencies
    try {
      this.topologicalSort(plan.steps);
    } catch (error) {
      errors.push(`Dependency error: ${error}`);
    }

    // Check if all tools exist
    for (const step of plan.steps) {
      if (!this.toolManager.getTool(step.tool)) {
        errors.push(`Unknown tool: ${step.tool} in step ${step.id}`);
      }
    }

    // Check for missing dependencies
    const stepIds = new Set(plan.steps.map(s => s.id));
    for (const step of plan.steps) {
      if (step.dependencies) {
        for (const depId of step.dependencies) {
          if (!stepIds.has(depId)) {
            errors.push(`Missing dependency: ${depId} for step ${step.id}`);
          }
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
