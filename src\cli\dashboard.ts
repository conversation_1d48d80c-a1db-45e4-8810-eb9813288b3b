import chalk from 'chalk';
import { ModernUI } from './modern-ui';
import { ChatSession, CLIConfig } from '../types';
import * as path from 'path';

export interface DashboardData {
  session: ChatSession | null;
  config: CLIConfig;
  contextFiles: number;
  totalTokens: number;
  uptime: number;
  lastActivity: Date;
  systemStatus: 'healthy' | 'warning' | 'error';
  activeOperations: string[];
  recentCommands: string[];
}

export class Dashboard {
  private ui: ModernUI;
  private lastRender: string = '';
  private isVisible: boolean = false;

  constructor(ui: ModernUI) {
    this.ui = ui;
  }

  public render(data: DashboardData): string {
    const { width, height } = this.ui.getTerminalSize();
    
    // Create main dashboard layout
    let output = '';

    // Header with branding
    output += this.renderHeader(data) + '\n';

    // Status cards in a grid layout
    output += this.renderStatusCards(data) + '\n';

    // Activity section
    if (height > 20) {
      output += this.renderActivity(data) + '\n';
    }

    // Quick stats
    output += this.renderQuickStats(data);

    this.lastRender = output;
    return output;
  }

  private renderHeader(data: DashboardData): string {
    const sessionId = data.session?.id.split('_')[1] || 'initializing';
    const modelName = data.session?.model.name || 'loading';
    const provider = data.session?.model.provider || 'unknown';
    const workingDir = path.basename(data.config.workingDirectory);

    // Create a modern header with gradient effect
    const title = '🤖 KRITRIMA AI';
    const subtitle = 'Advanced Local CLI Terminal LLM Interaction Environment';

    const headerBox = this.ui.createBox(
      `${chalk.bold.cyan(title)}\n${chalk.dim(subtitle)}\n\n` +
      `${chalk.white('Session:')} ${chalk.cyan(sessionId)} | ` +
      `${chalk.white('Model:')} ${chalk.green(modelName)} (${chalk.yellow(provider)}) | ` +
      `${chalk.white('Directory:')} ${chalk.blue(workingDir)}`,
      undefined,
      'rounded'
    );

    return headerBox;
  }

  private renderStatusCards(data: DashboardData): string {
    const { width } = this.ui.getTerminalSize();
    const cardWidth = Math.floor((width - 8) / 3); // 3 cards with spacing

    // Context Status Card
    const contextCard = this.createStatusCard(
      'Context',
      `${data.contextFiles} files`,
      `${data.totalTokens.toLocaleString()} tokens`,
      data.contextFiles > 0 ? 'success' : 'warning',
      '📁'
    );

    // Model Status Card
    const modelStatus = data.session?.model.name !== 'none' ? 'success' : 'error';
    const modelCard = this.createStatusCard(
      'AI Model',
      data.session?.model.name || 'None',
      data.session?.model.provider || 'N/A',
      modelStatus,
      '🧠'
    );

    // System Status Card
    const systemCard = this.createStatusCard(
      'System',
      data.systemStatus.toUpperCase(),
      this.formatUptime(data.uptime),
      data.systemStatus === 'healthy' ? 'success' : 
      data.systemStatus === 'warning' ? 'warning' : 'error',
      '⚡'
    );

    // Arrange cards horizontally if there's enough space
    if (width > 80) {
      return this.arrangeCardsHorizontally([contextCard, modelCard, systemCard]);
    } else {
      return [contextCard, modelCard, systemCard].join('\n\n');
    }
  }

  private createStatusCard(title: string, value: string, subtitle: string, status: 'success' | 'warning' | 'error', icon: string): string {
    const statusColors = {
      success: chalk.green,
      warning: chalk.yellow,
      error: chalk.red
    };

    const statusColor = statusColors[status];
    const content = `${icon} ${chalk.bold(title)}\n${statusColor(value)}\n${chalk.dim(subtitle)}`;

    return this.ui.createBox(content, undefined, 'rounded');
  }

  private arrangeCardsHorizontally(cards: string[]): string {
    const cardLines = cards.map(card => card.split('\n'));
    const maxLines = Math.max(...cardLines.map(lines => lines.length));
    
    let result = '';
    for (let i = 0; i < maxLines; i++) {
      const line = cardLines.map(lines => lines[i] || ' '.repeat(lines[0]?.length || 0)).join('  ');
      result += line + '\n';
    }
    
    return result.trim();
  }

  private renderActivity(data: DashboardData): string {
    let content = '';

    // Active Operations
    if (data.activeOperations.length > 0) {
      content += chalk.yellow('🔄 Active Operations:\n');
      for (const op of data.activeOperations) {
        content += `  • ${op}\n`;
      }
      content += '\n';
    }

    // Recent Commands
    if (data.recentCommands.length > 0) {
      content += chalk.blue('📝 Recent Commands:\n');
      for (const cmd of data.recentCommands.slice(-3)) {
        content += `  • ${chalk.dim(cmd)}\n`;
      }
    }

    if (content) {
      return this.ui.createBox(content.trim(), 'Activity', 'solid');
    }

    return '';
  }

  private renderQuickStats(data: DashboardData): string {
    const stats = [
      { label: 'Uptime', value: this.formatUptime(data.uptime), color: 'info' as const },
      { label: 'Last Activity', value: this.formatLastActivity(data.lastActivity), color: 'muted' as const },
      { label: 'Autonomy', value: data.config.autonomyLevel, color: 'accent' as const }
    ];

    const statusLine = this.ui.createStatusLine(stats);
    return chalk.dim('─'.repeat(this.ui.getTerminalSize().width - 2)) + '\n' + statusLine;
  }

  private formatUptime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  }

  private formatLastActivity(date: Date): string {
    const now = new Date();
    const diff = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diff < 60) {
      return 'Just now';
    } else if (diff < 3600) {
      return `${Math.floor(diff / 60)}m ago`;
    } else {
      return `${Math.floor(diff / 3600)}h ago`;
    }
  }

  public renderCompact(data: DashboardData): string {
    const sessionId = data.session?.id.split('_')[1] || 'init';
    const modelName = data.session?.model.name || 'none';
    const provider = data.session?.model.provider || 'unknown';
    const workingDir = path.basename(data.config.workingDirectory);

    return chalk.bgBlue.white.bold(' KRITRIMA AI ') +
           chalk.bgGray.white(` Session: ${sessionId} `) +
           chalk.bgGreen.white(` Model: ${modelName} (${provider}) `) +
           chalk.bgYellow.black(` Directory: ${workingDir} `) +
           chalk.bgMagenta.white(` Context: ${data.contextFiles} files `);
  }

  public renderMinimal(data: DashboardData): string {
    const contextStatus = data.contextFiles > 0 ? chalk.green('✓') : chalk.yellow('⚠');
    const modelStatus = data.session?.model.name !== 'none' ? chalk.green('✓') : chalk.red('✗');
    
    return `${chalk.cyan('kritrima')} ${contextStatus} ${modelStatus} ${chalk.dim(data.session?.model.name || 'none')}`;
  }

  public show(): void {
    this.isVisible = true;
  }

  public hide(): void {
    this.isVisible = false;
  }

  public isShowing(): boolean {
    return this.isVisible;
  }

  public getLastRender(): string {
    return this.lastRender;
  }

  // Real-time dashboard updates
  public createLiveDashboard(getData: () => DashboardData, interval: number = 1000): () => void {
    let updateInterval: NodeJS.Timeout;
    let isRunning = false;

    const update = () => {
      if (!this.isVisible) return;

      const data = getData();
      const newRender = this.render(data);
      
      if (newRender !== this.lastRender) {
        // Clear and redraw
        console.clear();
        console.log(newRender);
        this.lastRender = newRender;
      }
    };

    const start = () => {
      if (isRunning) return;
      isRunning = true;
      updateInterval = setInterval(update, interval);
    };

    const stop = () => {
      if (!isRunning) return;
      isRunning = false;
      clearInterval(updateInterval);
    };

    // Auto-start
    start();

    // Return stop function
    return stop;
  }

  // Interactive dashboard with keyboard controls
  public createInteractiveDashboard(getData: () => DashboardData): void {
    const readline = require('readline');
    
    // Set up keyboard input
    readline.emitKeypressEvents(process.stdin);
    if (process.stdin.isTTY) {
      process.stdin.setRawMode(true);
    }

    let currentView: 'full' | 'compact' | 'minimal' = 'full';

    const render = () => {
      const data = getData();
      console.clear();
      
      switch (currentView) {
        case 'full':
          console.log(this.render(data));
          break;
        case 'compact':
          console.log(this.renderCompact(data));
          break;
        case 'minimal':
          console.log(this.renderMinimal(data));
          break;
      }

      console.log(chalk.dim('\nControls: [f]ull [c]ompact [m]inimal [q]uit'));
    };

    const handleKeypress = (str: string, key: any) => {
      if (key.ctrl && key.name === 'c') {
        process.exit();
      }

      switch (key.name) {
        case 'f':
          currentView = 'full';
          render();
          break;
        case 'c':
          currentView = 'compact';
          render();
          break;
        case 'm':
          currentView = 'minimal';
          render();
          break;
        case 'q':
          process.stdin.setRawMode(false);
          process.stdin.removeListener('keypress', handleKeypress);
          console.clear();
          return;
      }
    };

    process.stdin.on('keypress', handleKeypress);
    render();
  }
}
