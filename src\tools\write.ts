import * as fs from 'fs';
import * as path from 'path';
import { Tool, ToolResult } from '../types';

export class WriteTool implements Tool {
  name = 'write';
  description = 'Create new files or completely overwrite existing files with new content. Supports various file types and encodings.';
  parameters = {
    type: 'object' as const,
    properties: {
      path: {
        type: 'string',
        description: 'The file path to write to'
      },
      content: {
        type: 'string',
        description: 'The content to write to the file'
      },
      encoding: {
        type: 'string',
        description: 'File encoding (default: utf8)',
        default: 'utf8'
      },
      mode: {
        type: 'string',
        enum: ['write', 'append', 'create'],
        description: 'Write mode: write (overwrite), append, or create (fail if exists)',
        default: 'write'
      },
      create_dirs: {
        type: 'boolean',
        description: 'Create parent directories if they don\'t exist',
        default: true
      },
      backup: {
        type: 'boolean',
        description: 'Create backup if file exists (for write mode)',
        default: false
      }
    },
    required: ['path', 'content']
  };

  async execute(args: {
    path: string;
    content: string;
    encoding?: string;
    mode?: string;
    create_dirs?: boolean;
    backup?: boolean;
  }): Promise<ToolResult> {
    try {
      const {
        path: filePath,
        content,
        encoding = 'utf8',
        mode = 'write',
        create_dirs = true,
        backup = false
      } = args;

      // Validate file path
      if (!filePath || filePath.trim() === '') {
        throw new Error('File path cannot be empty');
      }

      // Check if file exists
      const fileExists = fs.existsSync(filePath);

      // Handle different modes
      switch (mode) {
        case 'create':
          if (fileExists) {
            return {
              success: false,
              output: '',
              error: `File already exists: ${filePath}`
            };
          }
          break;

        case 'write':
          if (fileExists && backup) {
            await this.createBackup(filePath);
          }
          break;

        case 'append':
          return await this.appendToFile(filePath, content, encoding, create_dirs);
      }

      // Create parent directories if needed
      if (create_dirs) {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
      }

      // Write the file
      fs.writeFileSync(filePath, content, encoding as BufferEncoding);

      // Get file stats
      const stats = fs.statSync(filePath);

      return {
        success: true,
        output: `File written successfully: ${filePath}`,
        metadata: {
          path: filePath,
          size: stats.size,
          encoding,
          mode,
          created: !fileExists,
          lines: content.split('\n').length
        }
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message || 'Failed to write file'
      };
    }
  }

  private async appendToFile(
    filePath: string,
    content: string,
    encoding: string,
    create_dirs: boolean
  ): Promise<ToolResult> {
    try {
      // Create parent directories if needed
      if (create_dirs) {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
      }

      // Append to file
      fs.appendFileSync(filePath, content, encoding as BufferEncoding);

      // Get file stats
      const stats = fs.statSync(filePath);

      return {
        success: true,
        output: `Content appended to file: ${filePath}`,
        metadata: {
          path: filePath,
          size: stats.size,
          encoding,
          mode: 'append',
          appendedBytes: Buffer.byteLength(content, encoding as BufferEncoding)
        }
      };
    } catch (error: any) {
      throw new Error(`Failed to append to file: ${error.message}`);
    }
  }

  private async createBackup(filePath: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup.${timestamp}`;

    fs.copyFileSync(filePath, backupPath);
    return backupPath;
  }

  // Helper method to write multiple files
  async writeMultipleFiles(files: Array<{
    path: string;
    content: string;
    encoding?: string;
  }>): Promise<ToolResult> {
    const results: any[] = [];
    let successCount = 0;
    let errorCount = 0;

    for (const file of files) {
      try {
        const result = await this.execute({
          path: file.path,
          content: file.content,
          encoding: file.encoding
        });

        results.push({
          path: file.path,
          success: result.success,
          error: result.error
        });

        if (result.success) {
          successCount++;
        } else {
          errorCount++;
        }
      } catch (error: any) {
        results.push({
          path: file.path,
          success: false,
          error: error.message
        });
        errorCount++;
      }
    }

    return {
      success: errorCount === 0,
      output: `Wrote ${successCount} files successfully, ${errorCount} failed`,
      metadata: {
        totalFiles: files.length,
        successCount,
        errorCount,
        results
      }
    };
  }

  // Helper method to write from template
  async writeFromTemplate(
    templatePath: string,
    outputPath: string,
    variables: Record<string, string>
  ): Promise<ToolResult> {
    try {
      if (!fs.existsSync(templatePath)) {
        throw new Error(`Template file not found: ${templatePath}`);
      }

      let template = fs.readFileSync(templatePath, 'utf8');

      // Replace variables in template
      for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        template = template.replace(regex, value);
      }

      return await this.execute({
        path: outputPath,
        content: template
      });
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to write from template: ${error.message}`
      };
    }
  }

  // Helper method to write JSON
  async writeJSON(filePath: string, data: any, pretty: boolean = true): Promise<ToolResult> {
    try {
      const content = pretty ? JSON.stringify(data, null, 2) : JSON.stringify(data);

      return await this.execute({
        path: filePath,
        content
      });
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to write JSON: ${error.message}`
      };
    }
  }

  // Helper method to write CSV
  async writeCSV(filePath: string, data: any[][], headers?: string[]): Promise<ToolResult> {
    try {
      let content = '';

      if (headers) {
        content += headers.join(',') + '\n';
      }

      for (const row of data) {
        content += row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',') + '\n';
      }

      return await this.execute({
        path: filePath,
        content
      });
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: `Failed to write CSV: ${error.message}`
      };
    }
  }
}
